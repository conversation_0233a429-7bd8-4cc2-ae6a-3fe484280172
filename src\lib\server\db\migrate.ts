import { migrate } from 'drizzle-orm/postgres-js/migrator';
import { db } from './index';
import postgres from 'postgres';
import { env } from '$env/dynamic/private';

// 用于迁移的连接，迁移完成后会关闭
const migrationClient = postgres(env.DATABASE_URL!, { max: 1 });

async function runMigrations() {
	console.log('Running database migrations...');
	
	try {
		await migrate(db, { migrationsFolder: './drizzle' });
		console.log('Migrations completed successfully!');
	} catch (error) {
		console.error('Migration failed:', error);
		throw error;
	} finally {
		await migrationClient.end();
	}
}

// 如果直接运行此文件，执行迁移
if (import.meta.main) {
	runMigrations().catch(console.error);
}

export { runMigrations };
