// Formatting utilities

export function formatAge(age: number): string {
  return `${age}岁`;
}

export function formatDistance(distanceKm: number): string {
  if (distanceKm < 1) {
    return `${Math.round(distanceKm * 1000)}m`;
  } else if (distanceKm < 10) {
    return `${distanceKm.toFixed(1)}km`;
  } else {
    return `${Math.round(distanceKm)}km`;
  }
}

export function formatLastActive(lastActiveAt: Date): string {
  const now = new Date();
  const diffMs = now.getTime() - lastActiveAt.getTime();
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffMinutes < 1) {
    return '刚刚在线';
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前在线`;
  } else if (diffHours < 24) {
    return `${diffHours}小时前在线`;
  } else if (diffDays < 7) {
    return `${diffDays}天前在线`;
  } else {
    return '很久未在线';
  }
}

export function formatPoints(points: number): string {
  if (points >= 1000000) {
    return `${(points / 1000000).toFixed(1)}M`;
  } else if (points >= 1000) {
    return `${(points / 1000).toFixed(1)}K`;
  } else {
    return points.toString();
  }
}

export function formatKinkRating(rating: number): string {
  const ratingMap: Record<number, string> = {
    [-1]: '拒绝',
    [0]: '中立',
    [1]: '略感兴趣',
    [2]: '感兴趣',
    [3]: '很感兴趣',
    [4]: '非常感兴趣',
    [5]: '极度喜欢'
  };
  
  return ratingMap[rating] || '未知';
}

export function formatProfileCompleteness(score: number): string {
  if (score >= 90) return '完整';
  if (score >= 70) return '较完整';
  if (score >= 50) return '一般';
  if (score >= 30) return '不完整';
  return '很不完整';
}

export function formatVipLevel(level: number): string {
  const levelMap: Record<number, string> = {
    [0]: '普通用户',
    [1]: 'Pro 用户',
    [2]: 'VIP 用户',
    [3]: 'SVIP 用户'
  };
  
  return levelMap[level] || '未知等级';
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
}

export function formatPhoneNumber(phone: string): string {
  // 简单的手机号格式化（中国）
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 11 && cleaned.startsWith('1')) {
    return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 7)} ${cleaned.slice(7)}`;
  }
  return phone;
}

export function formatDateTime(date: Date): string {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
}

export function formatRelativeTime(date: Date): string {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffSeconds < 60) {
    return '刚刚';
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`;
  } else if (diffHours < 24) {
    return `${diffHours}小时前`;
  } else if (diffDays < 30) {
    return `${diffDays}天前`;
  } else {
    return formatDateTime(date);
  }
}
