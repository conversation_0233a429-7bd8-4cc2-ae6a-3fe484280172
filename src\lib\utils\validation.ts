// Validation utilities

import { APP_CONFIG } from './constants';

export function validateNickname(nickname: string): string | null {
  if (!nickname || nickname.trim().length === 0) {
    return '昵称不能为空';
  }
  
  const trimmed = nickname.trim();
  if (trimmed.length < APP_CONFIG.profile.min_nickname_length) {
    return `昵称至少需要 ${APP_CONFIG.profile.min_nickname_length} 个字符`;
  }
  
  if (trimmed.length > APP_CONFIG.profile.max_nickname_length) {
    return `昵称不能超过 ${APP_CONFIG.profile.max_nickname_length} 个字符`;
  }
  
  // 检查是否包含不当内容（简单版本）
  const inappropriateWords = ['admin', 'bot', 'system', 'null', 'undefined'];
  if (inappropriateWords.some(word => trimmed.toLowerCase().includes(word))) {
    return '昵称包含不允许的内容';
  }
  
  return null;
}

export function validateAge(age: number): string | null {
  if (!age || isNaN(age)) {
    return '请输入有效的年龄';
  }
  
  if (age < APP_CONFIG.profile.min_age) {
    return `年龄必须大于等于 ${APP_CONFIG.profile.min_age} 岁`;
  }
  
  if (age > APP_CONFIG.profile.max_age) {
    return `年龄不能超过 ${APP_CONFIG.profile.max_age} 岁`;
  }
  
  return null;
}

export function validateBio(bio: string): string | null {
  if (!bio) return null; // Bio 是可选的
  
  if (bio.length > APP_CONFIG.profile.max_bio_length) {
    return `个人简介不能超过 ${APP_CONFIG.profile.max_bio_length} 个字符`;
  }
  
  return null;
}

export function validateKinkRating(rating: number): string | null {
  if (rating < -1 || rating > 5) {
    return '评分必须在 -1 到 5 之间';
  }
  
  return null;
}

export function validateTelegramInitData(initData: string): boolean {
  // 简化版本的 Telegram initData 验证
  // 在生产环境中，你需要使用 bot token 来验证 hash
  try {
    const urlParams = new URLSearchParams(initData);
    const hash = urlParams.get('hash');
    const authDate = urlParams.get('auth_date');
    
    if (!hash || !authDate) {
      return false;
    }
    
    // 检查时间戳是否在合理范围内（24小时内）
    const authTimestamp = parseInt(authDate) * 1000;
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    
    if (now - authTimestamp > maxAge) {
      return false;
    }
    
    return true;
  } catch {
    return false;
  }
}

export function sanitizeInput(input: string): string {
  return input
    .trim()
    .replace(/[<>]/g, '') // 移除潜在的 HTML 标签
    .replace(/javascript:/gi, '') // 移除 JavaScript 协议
    .substring(0, 1000); // 限制长度
}

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}
