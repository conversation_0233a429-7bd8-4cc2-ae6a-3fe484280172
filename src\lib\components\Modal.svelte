<script lang="ts">
	import { modalStore, uiActions } from '$lib/stores';

	$: modal = $modalStore;

	function handleBackdropClick(event: MouseEvent) {
		if (event.target === event.currentTarget) {
			uiActions.modal.close();
		}
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			uiActions.modal.close();
		}
	}
</script>

{#if modal.isOpen}
	<!-- svelte-ignore a11y-no-static-element-interactions -->
	<div
		class="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black p-4"
		onclick={handleBackdropClick}
		onkeydown={handleKeydown}
		role="dialog"
		aria-modal="true"
		tabindex="-1"
	>
		<div
			class="max-h-[90vh] w-full max-w-md overflow-y-auto rounded-lg bg-white shadow-xl dark:bg-gray-800"
		>
			<!-- Modal Header -->
			<div
				class="flex items-center justify-between border-b border-gray-200 p-4 dark:border-gray-700"
			>
				<h3 class="text-lg font-semibold text-gray-900 dark:text-white">
					{#if modal.type === 'onboarding'}
						欢迎使用 BlueX
					{:else if modal.type === 'basic_profile_setup'}
						完善基础资料
					{:else if modal.type === 'advanced_profile_setup'}
						高级资料设置
					{:else if modal.type === 'kink_preferences'}
						Kink 偏好设置
					{:else if modal.type === 'privacy_settings'}
						隐私设置
					{:else if modal.type === 'search_filters'}
						搜索过滤器
					{:else if modal.type === 'user_profile'}
						用户资料
					{:else if modal.type === 'match_confirmation'}
						匹配确认
					{:else}
						提示
					{/if}
				</h3>
				<button
					onclick={() => uiActions.modal.close()}
					class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
					aria-label="关闭对话框"
				>
					<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M6 18L18 6M6 6l12 12"
						></path>
					</svg>
				</button>
			</div>

			<!-- Modal Content -->
			<div class="p-4">
				{#if modal.type === 'onboarding'}
					<div class="space-y-4 text-center">
						<div class="text-4xl">👋</div>
						<p class="text-gray-600 dark:text-gray-300">
							欢迎来到 BlueX！我们需要收集一些基础信息来为您提供更好的匹配体验。
						</p>
						<p class="text-sm text-gray-500 dark:text-gray-400">
							所有信息都是绝对隐私的，仅用于匹配算法。
						</p>
					</div>
				{:else if modal.type === 'basic_profile_setup'}
					<div class="space-y-4">
						<p class="text-gray-600 dark:text-gray-300">请填写基础资料以开始使用搜索功能。</p>
						<!-- 这里会渲染具体的表单组件 -->
					</div>
				{:else if modal.type === 'advanced_profile_setup'}
					<div class="space-y-4">
						<p class="text-gray-600 dark:text-gray-300">
							为了使用高级搜索功能，我们需要了解您的详细偏好。
						</p>
						<!-- 这里会渲染高级设置表单 -->
					</div>
				{:else}
					<div class="text-gray-600 dark:text-gray-300">
						{modal.data?.message || '内容加载中...'}
					</div>
				{/if}
			</div>

			<!-- Modal Footer -->
			<div class="flex justify-end space-x-3 border-t border-gray-200 p-4 dark:border-gray-700">
				<button
					onclick={() => uiActions.modal.close()}
					class="px-4 py-2 text-gray-600 transition-colors hover:text-gray-800 dark:text-gray-300 dark:hover:text-white"
				>
					取消
				</button>
				<button
					onclick={() => uiActions.modal.confirm(modal.data)}
					class="rounded-lg bg-blue-500 px-4 py-2 text-white transition-colors hover:bg-blue-600"
				>
					确认
				</button>
			</div>
		</div>
	</div>
{/if}

<style>
	/* 确保在移动设备上正确显示 */
	@media (max-height: 600px) {
		.max-h-\[90vh\] {
			max-height: 80vh;
		}
	}
</style>
