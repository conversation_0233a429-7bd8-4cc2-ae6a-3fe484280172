import { createHmac } from 'crypto';

/**
 * Telegram WebApp 数据验证器
 * 用于验证从 Telegram WebApp 接收的数据的真实性
 */

export interface TelegramWebAppData {
  user?: {
    id: number;
    first_name: string;
    last_name?: string;
    username?: string;
    language_code?: string;
    allows_write_to_pm?: boolean;
    photo_url?: string;
  };
  chat_instance?: string;
  chat_type?: string;
  auth_date: number;
  hash: string;
  start_param?: string;
}

/**
 * 验证 Telegram WebApp 初始化数据
 * @param initData - 原始的初始化数据字符串
 * @param botToken - Telegram Bot Token
 * @returns 验证结果
 */
export function validateTelegramWebAppData(initData: string, botToken: string): {
  isValid: boolean;
  data?: TelegramWebAppData;
  error?: string;
} {
  try {
    // 解析初始化数据
    const urlParams = new URLSearchParams(initData);
    const hash = urlParams.get('hash');
    
    if (!hash) {
      return { isValid: false, error: 'Missing hash parameter' };
    }

    // 移除 hash 参数
    urlParams.delete('hash');
    
    // 按字母顺序排序参数
    const sortedParams = Array.from(urlParams.entries())
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([key, value]) => `${key}=${value}`)
      .join('\n');

    // 创建密钥
    const secretKey = createHmac('sha256', 'WebAppData').update(botToken).digest();
    
    // 计算预期的 hash
    const expectedHash = createHmac('sha256', secretKey).update(sortedParams).digest('hex');

    // 验证 hash
    if (hash !== expectedHash) {
      return { isValid: false, error: 'Invalid hash' };
    }

    // 检查时间戳（可选，防止重放攻击）
    const authDate = parseInt(urlParams.get('auth_date') || '0');
    const now = Math.floor(Date.now() / 1000);
    const maxAge = 24 * 60 * 60; // 24小时

    if (now - authDate > maxAge) {
      return { isValid: false, error: 'Data is too old' };
    }

    // 解析用户数据
    const userData = urlParams.get('user');
    let user;
    if (userData) {
      try {
        user = JSON.parse(decodeURIComponent(userData));
      } catch {
        return { isValid: false, error: 'Invalid user data' };
      }
    }

    const data: TelegramWebAppData = {
      user,
      chat_instance: urlParams.get('chat_instance') || undefined,
      chat_type: urlParams.get('chat_type') || undefined,
      auth_date: authDate,
      hash,
      start_param: urlParams.get('start_param') || undefined
    };

    return { isValid: true, data };

  } catch (error) {
    return { 
      isValid: false, 
      error: error instanceof Error ? error.message : 'Validation failed' 
    };
  }
}

/**
 * 简化的验证函数，用于开发环境
 * 在生产环境中应该使用完整的验证
 */
export function validateTelegramWebAppDataSimple(initData: string): {
  isValid: boolean;
  data?: TelegramWebAppData;
  error?: string;
} {
  try {
    const urlParams = new URLSearchParams(initData);
    
    // 基本检查
    const authDate = parseInt(urlParams.get('auth_date') || '0');
    const hash = urlParams.get('hash');
    
    if (!hash || !authDate) {
      return { isValid: false, error: 'Missing required parameters' };
    }

    // 检查时间戳
    const now = Math.floor(Date.now() / 1000);
    const maxAge = 24 * 60 * 60; // 24小时

    if (now - authDate > maxAge) {
      return { isValid: false, error: 'Data is too old' };
    }

    // 解析用户数据
    const userData = urlParams.get('user');
    let user;
    if (userData) {
      try {
        user = JSON.parse(decodeURIComponent(userData));
        
        // 基本用户数据验证
        if (!user.id || !user.first_name) {
          return { isValid: false, error: 'Invalid user data structure' };
        }
      } catch {
        return { isValid: false, error: 'Invalid user data format' };
      }
    }

    const data: TelegramWebAppData = {
      user,
      chat_instance: urlParams.get('chat_instance') || undefined,
      chat_type: urlParams.get('chat_type') || undefined,
      auth_date: authDate,
      hash,
      start_param: urlParams.get('start_param') || undefined
    };

    return { isValid: true, data };

  } catch (error) {
    return { 
      isValid: false, 
      error: error instanceof Error ? error.message : 'Validation failed' 
    };
  }
}

/**
 * 从环境变量获取 Bot Token
 */
export function getBotToken(): string | null {
  return process.env.TELEGRAM_BOT_TOKEN || null;
}

/**
 * 验证 Telegram WebApp 数据的主函数
 * 根据环境自动选择验证方式
 */
export function validateTelegramData(initData: string): {
  isValid: boolean;
  data?: TelegramWebAppData;
  error?: string;
} {
  const botToken = getBotToken();
  
  if (botToken && process.env.NODE_ENV === 'production') {
    // 生产环境使用完整验证
    return validateTelegramWebAppData(initData, botToken);
  } else {
    // 开发环境使用简化验证
    console.warn('⚠️ 使用简化的Telegram数据验证（开发环境）');
    return validateTelegramWebAppDataSimple(initData);
  }
}
