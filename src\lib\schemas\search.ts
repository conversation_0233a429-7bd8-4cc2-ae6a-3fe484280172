import { z } from 'zod';

// 基础搜索过滤器 Schema
export const basicSearchFiltersSchema = z.object({
	ageRange: z
		.tuple([z.number().int().min(18), z.number().int().max(99)])
		.optional()
		.refine(
			(range) => !range || range[0] <= range[1],
			'最小年龄不能大于最大年龄'
		),
	
	location: z.object({
		country: z.string().optional(),
		city: z.string().optional(),
		radius: z.number().int().min(1).max(500).optional()
	}).optional(),
	
	orientation: z
		.array(z.enum([
			'straight',
			'gay',
			'lesbian',
			'bisexual',
			'asexual',
			'demisexual',
			'pansexual',
			'queer',
			'fluid',
			'other_orientation',
			'prefer_not_to_say_orientation'
		]))
		.optional(),
	
	bodyType: z
		.array(z.enum(['male_body', 'female_body', 'other_body_type']))
		.optional(),
	
	presentationStyle: z
		.array(z.enum([
			'conventional_masculine',
			'rugged_masculine',
			'feminine',
			'androgynous_neutral',
			'other_presentation_style'
		]))
		.optional()
});

// 高级搜索过滤器 Schema
export const advancedSearchFiltersSchema = basicSearchFiltersSchema.extend({
	kinkCategories: z
		.array(z.number().int().positive())
		.optional(),
	
	kinkRatings: z
		.record(
			z.string(),
			z.object({
				min: z.number().int().min(-1).max(5).optional(),
				max: z.number().int().min(-1).max(5).optional()
			}).refine(
				(rating) => !rating.min || !rating.max || rating.min <= rating.max,
				'最小评分不能大于最大评分'
			)
		)
		.optional(),
	
	relationshipStatus: z
		.array(z.enum([
			'single',
			'in_a_relationship',
			'complicated',
			'open_relationship',
			'married',
			'polyamorous',
			'other_relationship_status',
			'prefer_not_to_say_relationship_status'
		]))
		.optional(),
	
	profileCompleteness: z.object({
		minScore: z.number().int().min(0).max(100).optional(),
		hasAvatar: z.boolean().optional(),
		isVerified: z.boolean().optional()
	}).optional(),
	
	trustScore: z.object({
		min: z.number().int().min(0).max(1000).optional(),
		max: z.number().int().min(0).max(1000).optional()
	}).optional().refine(
		(score) => !score?.min || !score?.max || score.min <= score.max,
		'最小信誉分不能大于最大信誉分'
	)
});

// 超级搜索过滤器 Schema
export const superSearchFiltersSchema = advancedSearchFiltersSchema.extend({
	vipLevel: z
		.array(z.number().int().min(0).max(10))
		.optional(),
	
	lastActiveWithin: z
		.number()
		.int()
		.min(1)
		.max(8760) // 最多一年
		.optional(),
	
	excludeAlreadyInteracted: z.boolean().optional()
});

// 搜索选项 Schema
export const searchOptionsSchema = z.object({
	mode: z.enum(['basic', 'advanced', 'super'], {
		errorMap: () => ({ message: '请选择有效的搜索模式' })
	}),
	
	filters: z.union([
		basicSearchFiltersSchema,
		advancedSearchFiltersSchema,
		superSearchFiltersSchema
	]),
	
	sortBy: z.enum(['relevance', 'distance', 'lastActive', 'trustScore']).optional(),
	
	limit: z
		.number()
		.int()
		.min(1)
		.max(100)
		.optional()
		.default(20),
	
	offset: z
		.number()
		.int()
		.min(0)
		.optional()
		.default(0)
});

// 搜索结果 Schema
export const searchResultSchema = z.object({
	userId: z.string().uuid(),
	matchScore: z.number().min(0).max(100).optional(),
	distance: z.number().min(0).optional(),
	commonKinks: z.array(z.string()).optional(),
	isOnline: z.boolean().optional()
});

export const searchResponseSchema = z.object({
	results: z.array(searchResultSchema),
	total: z.number().int().min(0),
	hasMore: z.boolean(),
	searchId: z.string().optional()
});

// 类型导出
export type BasicSearchFilters = z.infer<typeof basicSearchFiltersSchema>;
export type AdvancedSearchFilters = z.infer<typeof advancedSearchFiltersSchema>;
export type SuperSearchFilters = z.infer<typeof superSearchFiltersSchema>;
export type SearchOptions = z.infer<typeof searchOptionsSchema>;
export type SearchResult = z.infer<typeof searchResultSchema>;
export type SearchResponse = z.infer<typeof searchResponseSchema>;
