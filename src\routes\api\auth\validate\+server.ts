import { json } from '@sveltejs/kit';
import type { <PERSON><PERSON>H<PERSON><PERSON> } from './$types';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';

export const POST: RequestHandler = async ({ request, cookies }) => {
	try {
		const authHeader = request.headers.get('authorization');
		const sessionCookie = cookies.get('session');
		
		// 从 Authorization header 或 cookie 中获取 session token
		let sessionToken: string | null = null;
		
		if (authHeader && authHeader.startsWith('Bearer ')) {
			sessionToken = authHeader.substring(7);
		} else if (sessionCookie) {
			sessionToken = sessionCookie;
		}

		if (!sessionToken) {
			return json({ error: 'No session token provided' }, { status: 401 });
		}

		// 简化的会话验证 - 在生产环境中应该使用更安全的方法
		// 从 session token 中提取 telegram user id
		const tokenParts = sessionToken.split('_');
		if (tokenParts.length < 3 || tokenParts[0] !== 'session') {
			return json({ error: 'Invalid session token' }, { status: 401 });
		}

		const telegramUserId = parseInt(tokenParts[1]);
		if (isNaN(telegramUserId)) {
			return json({ error: 'Invalid session token' }, { status: 401 });
		}

		// 查找用户
		const existingUsers = await db
			.select()
			.from(users)
			.where(eq(users.telegramUserId, telegramUserId))
			.limit(1);

		const user = existingUsers[0];

		if (!user) {
			return json({ error: 'User not found' }, { status: 404 });
		}

		// 检查用户是否被禁用
		if (user.isBanned || !user.isActive) {
			return json({ error: 'User account is disabled' }, { status: 403 });
		}

		// 更新最后活跃时间
		await db
			.update(users)
			.set({
				lastActiveAt: new Date(),
				updatedAt: new Date()
			})
			.where(eq(users.id, user.id));

		return json({
			user: {
				id: user.id,
				nickname: user.nickname,
				profileCompletenessScore: user.profileCompletenessScore,
				pointBalance: user.pointBalance,
				vipLevel: user.vipLevel,
				isVerified: user.isVerified,
				trustScore: user.trustScore,
				kinkMapCode: user.kinkMapCode,
				hasAvatar: user.hasAvatar,
				age: user.age,
				orientation: user.orientation,
				bodyType: user.bodyType,
				presentationStyle: user.presentationStyle,
				bio: user.bio,
				country: user.country,
				city: user.city,
				profileImageUrl: user.profileImageUrl,
				telegramUserId: user.telegramUserId,
				telegramUsername: user.telegramUsername
			}
		});
	} catch (error) {
		console.error('Session validation error:', error);
		return json({ error: 'Session validation failed' }, { status: 500 });
	}
};
