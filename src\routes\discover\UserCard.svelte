<script lang="ts">
	import { uiActions } from '$lib/stores';
	import { formatAge, formatDistance, formatLastActive } from '$lib/utils/formatting';
	import type { UserProfile } from '$lib/types';

	interface Props {
		user: UserProfile;
		matchScore?: number;
		distance?: number;
		commonKinks?: string[];
		isOnline?: boolean;
		searchMode: 'basic' | 'advanced' | 'super';
	}

	let { 
		user, 
		matchScore, 
		distance, 
		commonKinks = [], 
		isOnline = false, 
		searchMode 
	}: Props = $props();

	function handleUserClick() {
		uiActions.modal.open('user_profile', { user });
	}

	function handleLike() {
		// 实现点赞逻辑
		uiActions.toast.success('已发送喜欢');
	}

	function handlePass() {
		// 实现跳过逻辑
		uiActions.toast.info('已跳过');
	}

	function getMatchScoreColor(score?: number) {
		if (!score) return 'text-gray-500';
		if (score >= 80) return 'text-green-500';
		if (score >= 60) return 'text-yellow-500';
		return 'text-red-500';
	}

	function getMatchScoreLabel(score?: number) {
		if (!score) return '未知';
		if (score >= 80) return '高度匹配';
		if (score >= 60) return '中等匹配';
		return '低匹配度';
	}
</script>

<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
	<!-- 用户头部信息 -->
	<div class="p-4">
		<div class="flex items-start space-x-3">
			<!-- 头像 -->
			<div class="relative flex-shrink-0">
				{#if user.profileImageUrl}
					<img
						src={user.profileImageUrl}
						alt={user.nickname}
						class="w-12 h-12 rounded-full object-cover"
					/>
				{:else}
					<div class="w-12 h-12 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
						<span class="text-gray-600 dark:text-gray-300 text-lg">
							{user.nickname.charAt(0).toUpperCase()}
						</span>
					</div>
				{/if}
				
				<!-- 在线状态 -->
				{#if isOnline}
					<div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full"></div>
				{/if}
			</div>

			<!-- 用户信息 -->
			<div class="flex-1 min-w-0">
				<div class="flex items-center space-x-2">
					<h3 class="font-semibold text-gray-900 dark:text-white truncate">
						{user.nickname}
					</h3>
					{#if user.age}
						<span class="text-gray-600 dark:text-gray-300 text-sm">
							{formatAge(user.age)}
						</span>
					{/if}
					{#if user.isVerified}
						<span class="text-blue-500 text-sm">✓</span>
					{/if}
				</div>

				<!-- 位置和距离 -->
				<div class="flex items-center space-x-2 mt-1">
					{#if user.city || user.country}
						<span class="text-gray-600 dark:text-gray-300 text-sm">
							{user.city || user.country}
						</span>
					{/if}
					{#if distance}
						<span class="text-gray-500 dark:text-gray-400 text-sm">
							• {formatDistance(distance)}
						</span>
					{/if}
				</div>

				<!-- 最后活跃时间 -->
				<div class="text-gray-500 dark:text-gray-400 text-xs mt-1">
					{formatLastActive(user.lastActiveAt)}
				</div>
			</div>

			<!-- 匹配分数 -->
			{#if matchScore && searchMode !== 'basic'}
				<div class="text-right">
					<div class="text-sm font-medium {getMatchScoreColor(matchScore)}">
						{matchScore}%
					</div>
					<div class="text-xs text-gray-500 dark:text-gray-400">
						{getMatchScoreLabel(matchScore)}
					</div>
				</div>
			{/if}
		</div>

		<!-- 个人简介 -->
		{#if user.bio}
			<p class="text-gray-700 dark:text-gray-300 text-sm mt-3 line-clamp-2">
				{user.bio}
			</p>
		{/if}

		<!-- 共同兴趣 -->
		{#if commonKinks.length > 0 && searchMode !== 'basic'}
			<div class="mt-3">
				<div class="text-xs text-gray-600 dark:text-gray-400 mb-1">
					共同兴趣:
				</div>
				<div class="flex flex-wrap gap-1">
					{#each commonKinks.slice(0, 3) as kink}
						<span class="px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 text-xs rounded-full">
							{kink}
						</span>
					{/each}
					{#if commonKinks.length > 3}
						<span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs rounded-full">
							+{commonKinks.length - 3}
						</span>
					{/if}
				</div>
			</div>
		{/if}

		<!-- 用户标签 -->
		<div class="flex flex-wrap gap-2 mt-3">
			{#if user.orientation}
				<span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded">
					{user.orientation}
				</span>
			{/if}
			{#if user.relationshipStatus}
				<span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded">
					{user.relationshipStatus}
				</span>
			{/if}
			{#if user.vipLevel > 0}
				<span class="px-2 py-1 bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 text-xs rounded">
					VIP {user.vipLevel}
				</span>
			{/if}
		</div>
	</div>

	<!-- 操作按钮 -->
	<div class="flex border-t border-gray-200 dark:border-gray-700">
		<button
			onclick={handlePass}
			class="flex-1 py-3 text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
		>
			跳过
		</button>
		<button
			onclick={handleUserClick}
			class="flex-1 py-3 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors border-l border-r border-gray-200 dark:border-gray-700"
		>
			查看详情
		</button>
		<button
			onclick={handleLike}
			class="flex-1 py-3 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
		>
			喜欢 ❤️
		</button>
	</div>
</div>

<style>
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
