{"name": "bluex", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "db:start": "docker compose up", "db:push": "drizzle-kit push", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@internationalized/date": "^3.8.1", "@lucide/svelte": "^0.515.0", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.0", "@types/node": "^22", "autoprefixer": "^10.4.21", "drizzle-kit": "^0.31.1", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "formsnap": "^2.0.1", "globals": "^16.0.0", "postcss": "^8.5.6", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.10", "tw-animate-css": "^1.3.4", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^6.2.6", "vite-plugin-devtools-json": "^0.2.0"}, "dependencies": {"@auth/sveltekit": "^1.10.0", "@inlang/paraglide-js": "^2.0.0", "@telegram-apps/bridge": "^2.9.0", "@telegram-apps/sdk": "^3.11.0", "@telegram-apps/sdk-svelte": "^2.0.26", "@telegram-apps/signals": "^1.1.2", "@vitejs/plugin-basic-ssl": "^2.0.0", "bits-ui": "^2.8.8", "clsx": "^2.1.1", "drizzle-orm": "^0.44.2", "nanoid": "^5.1.5", "pg": "^8.16.2", "postgres": "^3.4.5", "sveltekit-superforms": "^2.26.1", "tailwind-merge": "^3.3.1", "vite-plugin-mkcert": "^1.17.8", "zod": "^3.25.67"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}}