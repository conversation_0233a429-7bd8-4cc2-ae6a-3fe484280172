// Debug utilities for TMA development

export interface DebugInfo {
	isTelegramWebApp: boolean;
	hasWebAppData: boolean;
	hasSessionData: boolean;
	hasUrlParams: boolean;
	userAgent: string;
	hostname: string;
	telegramData?: any;
	errors: string[];
}

export function getTelegramDebugInfo(): DebugInfo {
	const errors: string[] = [];
	const info: DebugInfo = {
		isTelegramWebApp: false,
		hasWebAppData: false,
		hasSessionData: false,
		hasUrlParams: false,
		userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'N/A',
		hostname: typeof window !== 'undefined' ? window.location.hostname : 'N/A',
		errors
	};

	if (typeof window === 'undefined') {
		errors.push('Window object not available (SSR)');
		return info;
	}

	// 检查是否在 Telegram WebApp 环境中
	try {
		if (window.Telegram?.WebApp) {
			info.isTelegramWebApp = true;
			info.hasWebAppData = !!window.Telegram.WebApp.initDataUnsafe?.user;
			if (info.hasWebAppData) {
				info.telegramData = {
					source: 'WebApp',
					user: window.Telegram.WebApp.initDataUnsafe.user,
					initData: window.Telegram.WebApp.initData
				};
			}
		}
	} catch (error) {
		errors.push(`WebApp check failed: ${error}`);
	}

	// 检查 URL 参数
	try {
		const urlParams = new URLSearchParams(window.location.search);
		const tgWebAppData = urlParams.get('tgWebAppData');
		if (tgWebAppData) {
			info.hasUrlParams = true;
			const decodedData = decodeURIComponent(tgWebAppData);
			const params = new URLSearchParams(decodedData);
			const userStr = params.get('user');
			if (userStr) {
				info.telegramData = {
					source: 'URL',
					user: JSON.parse(decodeURIComponent(userStr)),
					rawData: decodedData
				};
			}
		}
	} catch (error) {
		errors.push(`URL params check failed: ${error}`);
	}

	// 检查 sessionStorage (多种可能的键名)
	try {
		const possibleKeys = [
			'telegram-web-app-data',
			'tgWebAppData',
			'__telegram__initData',
			'telegram_init_data'
		];

		for (const key of possibleKeys) {
			const sessionData = sessionStorage.getItem(key);
			if (sessionData) {
				info.hasSessionData = true;

				// 尝试解析不同格式的数据
				try {
					// 格式1: URL编码的查询字符串
					const params = new URLSearchParams(sessionData);
					const userStr = params.get('user');
					if (userStr && !info.telegramData) {
						info.telegramData = {
							source: `SessionStorage(${key})`,
							user: JSON.parse(decodeURIComponent(userStr)),
							rawData: sessionData
						};
						break;
					}
				} catch {
					// 格式2: 直接的JSON字符串
					try {
						const parsed = JSON.parse(sessionData);
						if (parsed.user && !info.telegramData) {
							info.telegramData = {
								source: `SessionStorage(${key})`,
								user: parsed.user,
								rawData: sessionData
							};
							break;
						}
					} catch {
						// 忽略解析错误，继续尝试下一个键
					}
				}
			}
		}

		// 特殊处理：检查是否有原始的Telegram数据在URL hash中
		if (window.location.hash && !info.telegramData) {
			const hash = window.location.hash.substring(1);
			if (hash.includes('user=')) {
				const params = new URLSearchParams(hash);
				const userStr = params.get('user');
				if (userStr) {
					info.telegramData = {
						source: 'URL Hash',
						user: JSON.parse(decodeURIComponent(userStr)),
						rawData: hash
					};
				}
			}
		}
	} catch (error) {
		errors.push(`SessionStorage check failed: ${error}`);
	}

	return info;
}

export function logTelegramDebugInfo(): void {
	const info = getTelegramDebugInfo();

	console.group('🔍 Telegram WebApp Debug Info');
	console.log('Environment:', {
		hostname: info.hostname,
		userAgent: info.userAgent,
		isTelegramWebApp: info.isTelegramWebApp
	});

	console.log('Data Sources:', {
		hasWebAppData: info.hasWebAppData,
		hasUrlParams: info.hasUrlParams,
		hasSessionData: info.hasSessionData
	});

	if (info.telegramData) {
		console.log('Telegram Data:', info.telegramData);
	}

	if (info.errors.length > 0) {
		console.warn('Errors:', info.errors);
	}

	console.groupEnd();
}

// 模拟 Telegram 数据（用于开发调试）
export function injectMockTelegramData(): void {
	if (typeof window === 'undefined') return;

	const mockUser = {
		id: 6662549305,
		first_name: 'Sher',
		last_name: 'Locked',
		language_code: 'zh-hans',
		allows_write_to_pm: true,
		photo_url:
			'https://t.me/i/userpic/320/Ddepy63fuyaEFYJihlJAK7hLpZg8faemv1v9KHbOpG0qfZbxe2eWH3aZRRmhCDFi.svg'
	};

	const mockInitData = `user=${encodeURIComponent(JSON.stringify(mockUser))}&chat_instance=6891755669185758424&chat_type=private&auth_date=1750668363&signature=Rj5w3NzMUu93NJ6CCqZWoQ45WdNY9eqYcastZ_JEjKheFVDZpfimbaqnPVUONCP1oOxGyMKXBoG0HrsvsHphDA&hash=81ac3f3d0dc817c6b3a87b31505be0867a27b9f9f6e67b9bd64b461dbcfee434`;

	// 注入到 sessionStorage
	try {
		sessionStorage.setItem('telegram-web-app-data', mockInitData);
		console.log('✅ Mock Telegram data injected to sessionStorage');
	} catch (error) {
		console.error('❌ Failed to inject mock data:', error);
	}

	// 如果没有 Telegram WebApp，创建一个模拟对象
	if (!window.Telegram?.WebApp) {
		window.Telegram = {
			WebApp: {
				initData: mockInitData,
				initDataUnsafe: {
					user: mockUser,
					chat_instance: '6891755669185758424',
					chat_type: 'private',
					auth_date: 1750668363,
					hash: '81ac3f3d0dc817c6b3a87b31505be0867a27b9f9f6e67b9bd64b461dbcfee434'
				},
				version: '7.0',
				platform: 'web',
				colorScheme: 'light',
				themeParams: {},
				isExpanded: false,
				viewportHeight: window.innerHeight,
				viewportStableHeight: window.innerHeight,
				headerColor: '#ffffff',
				backgroundColor: '#ffffff',
				isClosingConfirmationEnabled: false,
				ready: () => console.log('Mock Telegram WebApp ready'),
				expand: () => console.log('Mock Telegram WebApp expand'),
				close: () => console.log('Mock Telegram WebApp close'),
				sendData: (data: string) => console.log('Mock sendData:', data),
				openLink: (url: string) => window.open(url, '_blank'),
				openTelegramLink: (url: string) => window.open(url, '_blank'),
				showPopup: (params: any) => alert(params.message),
				showAlert: (message: string) => alert(message),
				showConfirm: (message: string) => confirm(message),
				showScanQrPopup: () => console.log('Mock showScanQrPopup'),
				closeScanQrPopup: () => console.log('Mock closeScanQrPopup'),
				readTextFromClipboard: () => console.log('Mock readTextFromClipboard'),
				requestWriteAccess: () => console.log('Mock requestWriteAccess'),
				requestContact: () => console.log('Mock requestContact'),
				onEvent: (eventType: string, handler: Function) => console.log('Mock onEvent:', eventType),
				offEvent: (eventType: string, handler: Function) => console.log('Mock offEvent:', eventType)
			}
		} as any;

		console.log('✅ Mock Telegram WebApp object created');
	}
}

// 开发环境自动注入模拟数据
if (
	typeof window !== 'undefined' &&
	(window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') &&
	!window.Telegram?.WebApp?.initDataUnsafe?.user
) {
	injectMockTelegramData();
}
