<script lang="ts">
	import { uiActions } from '$lib/stores';
	import { goto } from '$app/navigation';

	function startBasicSetup() {
		goto('/onboarding/basic');
	}

	function skipForNow() {
		// 跳过设置，但提醒用户功能受限
		uiActions.toast.warning('跳过设置后，搜索功能将受到限制');
	}
</script>

<div class="flex min-h-screen items-center justify-center p-4">
	<div class="w-full max-w-md">
		<!-- 欢迎卡片 -->
		<div class="rounded-lg bg-white p-6 text-center shadow-xl dark:bg-gray-800">
			<div class="mb-6">
				<div class="mb-4 text-6xl">👋</div>
				<h1 class="mb-2 text-2xl font-bold text-gray-900 dark:text-white">欢迎来到 BlueX！</h1>
				<p class="text-gray-600 dark:text-gray-300">发现志同道合的朋友</p>
			</div>

			<!-- 功能介绍 -->
			<div class="mb-8 space-y-4">
				<div class="flex items-center space-x-3 text-left">
					<div
						class="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900"
					>
						<span class="text-sm text-blue-600 dark:text-blue-400">🔍</span>
					</div>
					<div>
						<h3 class="font-medium text-gray-900 dark:text-white">高级搜索</h3>
						<p class="text-sm text-gray-600 dark:text-gray-300">精准匹配志同道合的朋友</p>
					</div>
				</div>

				<div class="flex items-center space-x-3 text-left">
					<div
						class="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-green-100 dark:bg-green-900"
					>
						<span class="text-sm text-green-600 dark:text-green-400">🔒</span>
					</div>
					<div>
						<h3 class="font-medium text-gray-900 dark:text-white">隐私保护</h3>
						<p class="text-sm text-gray-600 dark:text-gray-300">您的信息绝对安全私密</p>
					</div>
				</div>

				<div class="flex items-center space-x-3 text-left">
					<div
						class="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-purple-100 dark:bg-purple-900"
					>
						<span class="text-sm text-purple-600 dark:text-purple-400">⭐</span>
					</div>
					<div>
						<h3 class="font-medium text-gray-900 dark:text-white">积分系统</h3>
						<p class="text-sm text-gray-600 dark:text-gray-300">完成任务获得积分奖励</p>
					</div>
				</div>
			</div>

			<!-- 隐私说明 -->
			<div class="mb-6 rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20">
				<div class="flex items-start space-x-2">
					<div class="text-lg text-blue-500">🛡️</div>
					<div class="text-left">
						<h4 class="text-sm font-medium text-blue-900 dark:text-blue-100">隐私承诺</h4>
						<p class="mt-1 text-xs text-blue-700 dark:text-blue-300">
							我们收集的所有信息都是绝对隐私的，仅用于匹配算法，不会泄露给任何第三方。
						</p>
					</div>
				</div>
			</div>

			<!-- 操作按钮 -->
			<div class="space-y-3">
				<button
					onclick={startBasicSetup}
					class="w-full rounded-lg bg-blue-500 px-4 py-3 font-medium text-white transition-colors hover:bg-blue-600"
				>
					开始设置资料
				</button>

				<button
					onclick={skipForNow}
					class="w-full px-4 py-2 text-sm text-gray-600 transition-colors hover:text-gray-800 dark:text-gray-300 dark:hover:text-white"
				>
					暂时跳过
				</button>
			</div>

			<!-- 进度提示 -->
			<div class="mt-6 text-xs text-gray-500 dark:text-gray-400">设置基础资料只需要 2-3 分钟</div>
		</div>
	</div>
</div>
