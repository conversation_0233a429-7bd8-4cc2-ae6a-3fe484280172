import { z } from 'zod';

// 基础用户资料 Schema
export const basicProfileSchema = z.object({
	nickname: z
		.string()
		.min(2, '昵称至少需要 2 个字符')
		.max(20, '昵称不能超过 20 个字符')
		.regex(/^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/, '昵称只能包含字母、数字、中文、下划线和连字符'),
	
	age: z
		.number()
		.int('年龄必须是整数')
		.min(18, '年龄必须大于等于 18 岁')
		.max(99, '年龄不能超过 99 岁'),
	
	orientation: z.enum([
		'straight',
		'gay', 
		'lesbian',
		'bisexual',
		'asexual',
		'demisexual',
		'pansexual',
		'queer',
		'fluid',
		'other_orientation',
		'prefer_not_to_say_orientation'
	], {
		errorMap: () => ({ message: '请选择有效的性取向' })
	}),
	
	bodyType: z.enum([
		'male_body',
		'female_body', 
		'other_body_type'
	], {
		errorMap: () => ({ message: '请选择有效的身体类型' })
	}),
	
	presentationStyle: z.enum([
		'conventional_masculine',
		'rugged_masculine',
		'feminine',
		'androgynous_neutral',
		'other_presentation_style'
	], {
		errorMap: () => ({ message: '请选择有效的展现风格' })
	})
});

// 扩展用户资料 Schema
export const extendedProfileSchema = basicProfileSchema.extend({
	bio: z
		.string()
		.max(500, '个人简介不能超过 500 个字符')
		.optional(),
	
	heightCm: z
		.number()
		.int('身高必须是整数')
		.min(100, '身高不能小于 100cm')
		.max(250, '身高不能超过 250cm')
		.optional(),
	
	weightKg: z
		.number()
		.int('体重必须是整数')
		.min(30, '体重不能小于 30kg')
		.max(300, '体重不能超过 300kg')
		.optional(),
	
	relationshipStatus: z.enum([
		'single',
		'in_a_relationship',
		'complicated',
		'open_relationship',
		'married',
		'polyamorous',
		'other_relationship_status',
		'prefer_not_to_say_relationship_status'
	]).optional(),
	
	country: z
		.string()
		.min(1, '请输入国家')
		.max(50, '国家名称不能超过 50 个字符')
		.optional(),
	
	city: z
		.string()
		.max(50, '城市名称不能超过 50 个字符')
		.optional()
});

// Kink 偏好 Schema
export const kinkPreferencesSchema = z.object({
	kinkRatings: z.record(
		z.string(),
		z.number()
			.int('评分必须是整数')
			.min(-1, '评分不能小于 -1')
			.max(5, '评分不能大于 5')
	).optional(),
	
	kinkCategoryBitmask: z
		.number()
		.int('类别掩码必须是整数')
		.min(0, '类别掩码不能为负数')
		.optional()
});

// 隐私设置 Schema
export const privacySettingsSchema = z.object({
	blockVisibilityFromBodyTypes: z
		.array(z.enum(['male_body', 'female_body', 'other_body_type']))
		.optional(),
	
	blockVisibilityFromPresentationStyles: z
		.array(z.enum([
			'conventional_masculine',
			'rugged_masculine', 
			'feminine',
			'androgynous_neutral',
			'other_presentation_style'
		]))
		.optional(),
	
	blockVisibilityFromOrientations: z
		.array(z.enum([
			'straight',
			'gay',
			'lesbian', 
			'bisexual',
			'asexual',
			'demisexual',
			'pansexual',
			'queer',
			'fluid',
			'other_orientation',
			'prefer_not_to_say_orientation'
		]))
		.optional()
});

// 完整用户资料 Schema
export const fullProfileSchema = extendedProfileSchema
	.merge(kinkPreferencesSchema)
	.merge(privacySettingsSchema);

// 用户注册 Schema
export const userRegistrationSchema = z.object({
	telegramUserId: z.number().int().positive(),
	telegramUsername: z.string().optional(),
	referrerCode: z.string().optional()
});

// 类型导出
export type BasicProfile = z.infer<typeof basicProfileSchema>;
export type ExtendedProfile = z.infer<typeof extendedProfileSchema>;
export type KinkPreferences = z.infer<typeof kinkPreferencesSchema>;
export type PrivacySettings = z.infer<typeof privacySettingsSchema>;
export type FullProfile = z.infer<typeof fullProfileSchema>;
export type UserRegistration = z.infer<typeof userRegistrationSchema>;
