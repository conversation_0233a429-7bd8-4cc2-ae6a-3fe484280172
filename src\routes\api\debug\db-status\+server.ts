import { json } from '@sveltejs/kit';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { sql } from 'drizzle-orm';

export const GET: RequestHandler = async () => {
	try {
		const status = {
			connected: false,
			error: null as string | null,
			database: null as string | null,
			userCount: 0,
			timestamp: new Date().toISOString()
		};

		// 测试基本连接
		try {
			const result = await db.execute(sql`SELECT current_database(), version()`);
			if (result.rows && result.rows.length > 0) {
				status.connected = true;
				status.database = result.rows[0].current_database as string;
			}
		} catch (error) {
			status.error = error instanceof Error ? error.message : 'Database connection failed';
			return json(status);
		}

		// 测试表查询
		try {
			const userCountResult = await db.select({ count: sql<number>`count(*)` }).from(users);
			status.userCount = userCountResult[0]?.count || 0;
		} catch (error) {
			status.error = `Table query failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
		}

		return json(status);
	} catch (error) {
		return json({
			connected: false,
			error: error instanceof Error ? error.message : 'Unknown error',
			database: null,
			userCount: 0,
			timestamp: new Date().toISOString()
		});
	}
};
