import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import { generateKinkMapCode } from '$lib/utils/helpers';
import { nanoid } from 'nanoid';

export const POST: RequestHandler = async ({ request, cookies }) => {
	try {
		const body = await request.json();

		// 验证 Telegram 用户数据
		const { telegramUser, referrerCode } = body;

		if (!telegramUser || !telegramUser.id) {
			return json({ error: 'Invalid Telegram user data' }, { status: 400 });
		}

		// 检查用户是否已存在
		const existingUsers = await db
			.select()
			.from(users)
			.where(eq(users.telegramUserId, telegramUser.id))
			.limit(1);

		let user = existingUsers[0];

		if (!user) {
			// 创建新用户
			const newUserData = {
				telegramUserId: telegramUser.id,
				telegramUsername: telegramUser.username || null,
				nickname: telegramUser.first_name,
				kinkMapCode: generateKinkMapCode(),
				profileImageUrl: telegramUser.photo_url || null,
				hasAvatar: !!telegramUser.photo_url,
				profileCompletenessScore: 20, // 基础分数
				vipLevel: telegramUser.is_premium ? 1 : 0,
				pointBalance: 100, // 注册奖励
				trustScore: 50
			};

			const insertedUsers = await db.insert(users).values(newUserData).returning();

			user = insertedUsers[0];
		} else {
			// 更新现有用户的最后活跃时间
			const updatedUsers = await db
				.update(users)
				.set({
					lastActiveAt: new Date(),
					updatedAt: new Date()
				})
				.where(eq(users.id, user.id))
				.returning();

			user = updatedUsers[0];
		}

		// 生成会话令牌
		const sessionToken = `session_${telegramUser.id}_${nanoid()}`;

		// 设置 cookie（在生产环境中应该使用 httpOnly 和 secure）
		cookies.set('session', sessionToken, {
			path: '/',
			maxAge: 60 * 60 * 24 * 7, // 7 days
			sameSite: 'strict',
			httpOnly: true
		});

		return json({
			sessionToken,
			user: {
				id: user.id,
				nickname: user.nickname,
				profileCompletenessScore: user.profileCompletenessScore,
				pointBalance: user.pointBalance,
				vipLevel: user.vipLevel,
				isVerified: user.isVerified,
				trustScore: user.trustScore,
				kinkMapCode: user.kinkMapCode,
				hasAvatar: user.hasAvatar,
				age: user.age,
				orientation: user.orientation,
				bodyType: user.bodyType,
				presentationStyle: user.presentationStyle,
				bio: user.bio,
				country: user.country,
				city: user.city,
				profileImageUrl: user.profileImageUrl
			}
		});
	} catch (error) {
		console.error('Telegram auth error:', error);
		return json({ error: 'Authentication failed' }, { status: 500 });
	}
};
