import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import { generateKinkMapCode } from '$lib/utils/helpers';

export const POST: RequestHandler = async ({ request, cookies }) => {
	try {
		const body = await request.json();

		// 验证 Telegram 用户数据
		const { telegramUser, referrerCode } = body;

		if (!telegramUser || !telegramUser.id) {
			return json({ error: 'Invalid Telegram user data' }, { status: 400 });
		}

		// 检查用户是否已存在
		let user = mockUserDatabase.get(telegramUser.id);

		if (!user) {
			// 创建新用户
			user = {
				id: `user_${telegramUser.id}`,
				telegramUserId: telegramUser.id,
				telegramUsername: telegramUser.username,
				nickname: telegram<PERSON>ser.first_name,
				age: null,
				orientation: null,
				bodyType: null,
				presentationStyle: null,
				bio: null,
				country: null,
				city: null,
				profileImageUrl: telegramUser.photo_url,
				hasAvatar: !!telegramUser.photo_url,
				profileCompletenessScore: 20, // 基础分数
				isVerified: false,
				trustScore: 50,
				vipLevel: telegramUser.is_premium ? 1 : 0,
				pointBalance: 100, // 注册奖励
				kinkCategoryBitmask: 0,
				kinkRatings: {},
				blockVisibilityFromBodyTypes: [],
				blockVisibilityFromPresentationStyles: [],
				blockVisibilityFromOrientations: [],
				createdAt: new Date(),
				updatedAt: new Date(),
				lastActiveAt: new Date(),
				isActive: true,
				isBanned: false,
				kinkMapCode: generateKinkMapCode(),
				referrerCode
			};

			mockUserDatabase.set(telegramUser.id, user);
		} else {
			// 更新现有用户的最后活跃时间
			user.lastActiveAt = new Date();
			user.updatedAt = new Date();
		}

		// 生成会话令牌（简化版本）
		const sessionToken = `session_${telegramUser.id}_${Date.now()}`;

		// 设置 cookie（在生产环境中应该使用 httpOnly 和 secure）
		cookies.set('session', sessionToken, {
			path: '/',
			maxAge: 60 * 60 * 24 * 7, // 7 days
			sameSite: 'strict'
		});

		return json({
			sessionToken,
			user: {
				id: user.id,
				nickname: user.nickname,
				profileCompletenessScore: user.profileCompletenessScore,
				pointBalance: user.pointBalance,
				vipLevel: user.vipLevel,
				isVerified: user.isVerified,
				trustScore: user.trustScore,
				kinkMapCode: user.kinkMapCode
			}
		});
	} catch (error) {
		console.error('Telegram auth error:', error);
		return json({ error: 'Authentication failed' }, { status: 500 });
	}
};

function generateKinkMapCode(): string {
	const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
	let result = '';
	for (let i = 0; i < 8; i++) {
		result += chars.charAt(Math.floor(Math.random() * chars.length));
	}
	return result;
}
