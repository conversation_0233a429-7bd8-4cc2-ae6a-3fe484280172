import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';

export const GET: RequestHandler = async ({ params, cookies }) => {
	try {
		const userId = params.id;
		
		if (!userId) {
			return json({ error: 'User ID is required' }, { status: 400 });
		}

		// 验证会话
		const sessionCookie = cookies.get('session');
		if (!sessionCookie) {
			return json({ error: 'Unauthorized' }, { status: 401 });
		}

		// 查找用户
		const existingUsers = await db
			.select()
			.from(users)
			.where(eq(users.id, userId))
			.limit(1);

		const user = existingUsers[0];

		if (!user) {
			return json({ error: 'User not found' }, { status: 404 });
		}

		// 返回用户资料（不包含敏感信息）
		return json({
			id: user.id,
			nickname: user.nickname,
			profileCompletenessScore: user.profileCompletenessScore,
			pointBalance: user.pointBalance,
			vipLevel: user.vipLevel,
			isVerified: user.isVerified,
			trustScore: user.trustScore,
			kinkMapCode: user.kinkMapCode,
			hasAvatar: user.hasAvatar,
			age: user.age,
			orientation: user.orientation,
			bodyType: user.bodyType,
			presentationStyle: user.presentationStyle,
			bio: user.bio,
			country: user.country,
			city: user.city,
			profileImageUrl: user.profileImageUrl,
			telegramUserId: user.telegramUserId,
			telegramUsername: user.telegramUsername,
			createdAt: user.createdAt,
			updatedAt: user.updatedAt,
			lastActiveAt: user.lastActiveAt,
			isActive: user.isActive
		});
	} catch (error) {
		console.error('Get user error:', error);
		return json({ error: 'Failed to get user' }, { status: 500 });
	}
};
