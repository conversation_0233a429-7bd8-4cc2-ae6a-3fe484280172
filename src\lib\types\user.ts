// User-related type definitions

export type UserRegistrationStatus = 
  | 'new_user'           // 全新用户，需要基础资料
  | 'basic_complete'     // 基础资料完成，可以进行基础搜索
  | 'advanced_complete'  // 高级资料完成，可以进行高级搜索
  | 'fully_complete';    // 所有资料完成

export type ProfileCompletionStep = 
  | 'nickname'
  | 'age'
  | 'orientation'
  | 'body_type'
  | 'presentation_style'
  | 'location'
  | 'bio'
  | 'kink_preferences'
  | 'privacy_settings';

export interface UserProfile {
  id: string;
  telegramUserId: number;
  telegramUsername?: string;
  kinkMapCode: string;
  
  // 基础信息
  nickname: string;
  age?: number;
  orientation?: string;
  bodyType?: string;
  presentationStyle?: string;
  
  // 补充信息
  bio?: string;
  heightCm?: number;
  weightKg?: number;
  relationshipStatus?: string;
  country?: string;
  city?: string;
  profileImageUrl?: string;
  
  // 系统状态
  hasAvatar: boolean;
  profileCompletenessScore: number;
  isVerified: boolean;
  trustScore: number;
  
  // 商业化
  vipLevel: number;
  pointBalance: number;
  
  // Kink 偏好
  kinkCategoryBitmask: number;
  kinkRatings: Record<string, number>;
  
  // 隐私设置
  blockVisibilityFromBodyTypes?: string[];
  blockVisibilityFromPresentationStyles?: string[];
  blockVisibilityFromOrientations?: string[];
  
  // 时间戳
  createdAt: Date;
  updatedAt: Date;
  lastActiveAt: Date;
  isActive: boolean;
  isBanned: boolean;
}

export interface UserOnboardingState {
  status: UserRegistrationStatus;
  completedSteps: ProfileCompletionStep[];
  currentStep?: ProfileCompletionStep;
  isFirstTime: boolean;
  needsAdvancedSetup: boolean;
}
