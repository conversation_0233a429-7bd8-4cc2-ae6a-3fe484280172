<script lang="ts">
	import { userProfile } from '$lib/stores';
	import { formatPoints } from '$lib/utils/formatting';
	import { calculateSearchCost } from '$lib/utils/helpers';

	interface Props {
		searchMode: 'basic' | 'advanced' | 'super';
		canBasicSearch: boolean;
		canAdvancedSearch: boolean;
		canSuperSearch: boolean;
		onModeChange: (mode: 'basic' | 'advanced' | 'super') => void;
		onToggleFilters: () => void;
	}

	let {
		searchMode,
		canBasicSearch,
		canAdvancedSearch,
		canSuperSearch,
		onModeChange,
		onToggleFilters
	}: Props = $props();

	let user = $userProfile;
	let searchCost = calculateSearchCost(searchMode);

	function getModeLabel(mode: string) {
		switch (mode) {
			case 'basic':
				return '基础搜索';
			case 'advanced':
				return '高级搜索';
			case 'super':
				return '超级搜索';
			default:
				return '搜索';
		}
	}

	function getModeDescription(mode: string) {
		switch (mode) {
			case 'basic':
				return '基于基础信息的匹配';
			case 'advanced':
				return '基于详细偏好的精准匹配';
			case 'super':
				return 'AI 增强的智能匹配';
			default:
				return '';
		}
	}

	function canUseMode(mode: string) {
		switch (mode) {
			case 'basic':
				return canBasicSearch;
			case 'advanced':
				return canAdvancedSearch;
			case 'super':
				return canSuperSearch;
			default:
				return false;
		}
	}
</script>

<header class="border-b border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800">
	<div class="px-4 py-3">
		<!-- 顶部信息栏 -->
		<div class="mb-4 flex items-center justify-between">
			<div>
				<h1 class="text-xl font-bold text-gray-900 dark:text-white">发现</h1>
				<p class="text-sm text-gray-600 dark:text-gray-300">找到志同道合的朋友</p>
			</div>

			{#if user}
				<div class="text-right">
					<div class="text-sm font-medium text-gray-900 dark:text-white">
						{formatPoints(user.pointBalance)} 积分
					</div>
					<div class="text-xs text-gray-500 dark:text-gray-400">
						VIP {user.vipLevel}
					</div>
				</div>
			{/if}
		</div>

		<!-- 搜索模式选择 -->
		<div class="space-y-3">
			<div class="flex items-center justify-between">
				<span class="text-sm font-medium text-gray-700 dark:text-gray-300"> 搜索模式 </span>
				<button
					onclick={onToggleFilters}
					class="text-sm font-medium text-blue-500 hover:text-blue-600"
				>
					过滤器
				</button>
			</div>

			<div class="grid grid-cols-3 gap-2">
				{#each ['basic', 'advanced', 'super'] as mode}
					{@const isActive = searchMode === mode}
					{@const canUse = canUseMode(mode)}
					{@const cost = calculateSearchCost(mode as any)}

					<button
						onclick={() => canUse && onModeChange(mode as any)}
						disabled={!canUse}
						class="relative rounded-lg border p-3 text-center transition-all {isActive
							? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
							: canUse
								? 'border-gray-200 hover:border-gray-300 dark:border-gray-600 dark:hover:border-gray-500'
								: 'cursor-not-allowed border-gray-200 opacity-50 dark:border-gray-600'}"
					>
						<div
							class="text-sm font-medium {isActive
								? 'text-blue-700 dark:text-blue-300'
								: canUse
									? 'text-gray-900 dark:text-white'
									: 'text-gray-500 dark:text-gray-400'}"
						>
							{getModeLabel(mode)}
						</div>

						<div
							class="mt-1 text-xs {isActive
								? 'text-blue-600 dark:text-blue-400'
								: canUse
									? 'text-gray-600 dark:text-gray-300'
									: 'text-gray-400 dark:text-gray-500'}"
						>
							{getModeDescription(mode)}
						</div>

						<div
							class="mt-1 text-xs font-medium {isActive
								? 'text-blue-500 dark:text-blue-400'
								: canUse
									? 'text-gray-500 dark:text-gray-400'
									: 'text-gray-400 dark:text-gray-500'}"
						>
							{cost} 积分
						</div>

						{#if !canUse}
							<div
								class="bg-opacity-75 absolute inset-0 flex items-center justify-center rounded-lg bg-gray-50 dark:bg-gray-800"
							>
								<span class="text-xs text-gray-500 dark:text-gray-400">
									{mode === 'advanced' ? '需要完善资料' : '需要升级会员'}
								</span>
							</div>
						{/if}

						{#if isActive}
							<div class="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-blue-500"></div>
						{/if}
					</button>
				{/each}
			</div>
		</div>

		<!-- 搜索成本提示 -->
		{#if user && searchCost > 0}
			<div class="mt-3 rounded-lg bg-gray-50 p-2 dark:bg-gray-700">
				<div class="flex items-center justify-between text-sm">
					<span class="text-gray-600 dark:text-gray-300"> 本次搜索消耗 </span>
					<span class="font-medium text-gray-900 dark:text-white">
						{searchCost} 积分
					</span>
				</div>

				{#if user.pointBalance < searchCost}
					<div class="mt-1 text-xs text-red-600 dark:text-red-400">积分不足，请先获取更多积分</div>
				{/if}
			</div>
		{/if}
	</div>
</header>
