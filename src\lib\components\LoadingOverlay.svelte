<script lang="ts">
	import { loadingStore } from '$lib/stores';

	let loading = $derived(loadingStore);
</script>

{#if loading.isLoading}
	<div class="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black">
		<div class="mx-4 w-full max-w-sm rounded-lg bg-white p-6 shadow-xl dark:bg-gray-800">
			<div class="space-y-4 text-center">
				<!-- Loading Spinner -->
				<div class="relative">
					<div class="mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-blue-500"></div>
					{#if loading.progress !== undefined}
						<div class="absolute inset-0 flex items-center justify-center">
							<span class="text-xs font-medium text-blue-500">
								{Math.round(loading.progress)}%
							</span>
						</div>
					{/if}
				</div>

				<!-- Loading Message -->
				{#if loading.message}
					<p class="text-sm text-gray-600 dark:text-gray-300">
						{loading.message}
					</p>
				{:else}
					<p class="text-sm text-gray-600 dark:text-gray-300">加载中...</p>
				{/if}

				<!-- Progress Bar -->
				{#if loading.progress !== undefined}
					<div class="h-2 w-full rounded-full bg-gray-200 dark:bg-gray-700">
						<div
							class="h-2 rounded-full bg-blue-500 transition-all duration-300 ease-out"
							style="width: {loading.progress}%"
						></div>
					</div>
				{/if}
			</div>
		</div>
	</div>
{/if}
