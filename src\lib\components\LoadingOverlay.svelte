<script lang="ts">
	import { loadingStore } from '$lib/stores';

	$: loading = $loadingStore;
</script>

{#if loading.isLoading}
	<div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
		<div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 max-w-sm w-full mx-4">
			<div class="text-center space-y-4">
				<!-- Loading Spinner -->
				<div class="relative">
					<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
					{#if loading.progress !== undefined}
						<div class="absolute inset-0 flex items-center justify-center">
							<span class="text-xs font-medium text-blue-500">
								{Math.round(loading.progress)}%
							</span>
						</div>
					{/if}
				</div>
				
				<!-- Loading Message -->
				{#if loading.message}
					<p class="text-gray-600 dark:text-gray-300 text-sm">
						{loading.message}
					</p>
				{:else}
					<p class="text-gray-600 dark:text-gray-300 text-sm">
						加载中...
					</p>
				{/if}
				
				<!-- Progress Bar -->
				{#if loading.progress !== undefined}
					<div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
						<div 
							class="bg-blue-500 h-2 rounded-full transition-all duration-300 ease-out"
							style="width: {loading.progress}%"
						></div>
					</div>
				{/if}
			</div>
		</div>
	</div>
{/if}
