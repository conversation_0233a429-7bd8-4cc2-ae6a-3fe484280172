import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { basicProfileSchema, fullProfileSchema } from '$lib/schemas/user';

// 模拟用户数据存储（实际应该使用数据库）
const mockUserDatabase = new Map();

export const GET: RequestHandler = async ({ cookies }) => {
	try {
		const sessionToken = cookies.get('session');
		if (!sessionToken) {
			return json({ error: 'Unauthorized' }, { status: 401 });
		}

		// 从 session token 中提取用户 ID（简化版本）
		const telegramId = sessionToken.split('_')[1];
		const user = mockUserDatabase.get(parseInt(telegramId));

		if (!user) {
			return json({ error: 'User not found' }, { status: 404 });
		}

		return json(user);

	} catch (error) {
		console.error('Get profile error:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};

export const PATCH: RequestHandler = async ({ request, cookies }) => {
	try {
		const sessionToken = cookies.get('session');
		if (!sessionToken) {
			return json({ error: 'Unauthorized' }, { status: 401 });
		}

		const body = await request.json();
		
		// 验证更新数据
		const validation = fullProfileSchema.partial().safeParse(body);
		if (!validation.success) {
			return json(
				{ error: 'Invalid profile data', details: validation.error.errors },
				{ status: 400 }
			);
		}

		// 从 session token 中提取用户 ID
		const telegramId = sessionToken.split('_')[1];
		let user = mockUserDatabase.get(parseInt(telegramId));

		if (!user) {
			return json({ error: 'User not found' }, { status: 404 });
		}

		// 更新用户资料
		user = {
			...user,
			...validation.data,
			updatedAt: new Date()
		};

		// 重新计算资料完整度
		user.profileCompletenessScore = calculateProfileCompleteness(user);

		mockUserDatabase.set(parseInt(telegramId), user);

		return json(user);

	} catch (error) {
		console.error('Update profile error:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};

function calculateProfileCompleteness(user: any): number {
	const weights = {
		nickname: 15,
		age: 15,
		orientation: 10,
		bodyType: 10,
		presentationStyle: 10,
		bio: 10,
		country: 5,
		city: 5,
		profileImageUrl: 10,
		kinkRatings: 10
	};

	let score = 0;
	let maxScore = 0;

	for (const [field, weight] of Object.entries(weights)) {
		maxScore += weight;
		if (user[field]) {
			if (field === 'kinkRatings') {
				// 检查是否有至少 3 个 kink 评分
				const ratings = user.kinkRatings || {};
				if (Object.keys(ratings).length >= 3) {
					score += weight;
				}
			} else {
				score += weight;
			}
		}
	}

	return Math.round((score / maxScore) * 100);
}
