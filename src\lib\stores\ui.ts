// UI state store

import { writable, derived } from 'svelte/store';
import type { ModalState, ToastMessage, LoadingState, TabId } from '../types';

// Modal state
const initialModalState: ModalState = {
  isOpen: false,
  type: undefined,
  data: undefined,
  onClose: undefined,
  onConfirm: undefined
};

export const modalStore = writable<ModalState>(initialModalState);

// Toast messages
export const toastStore = writable<ToastMessage[]>([]);

// Loading state
export const loadingStore = writable<LoadingState>({
  isLoading: false,
  message: undefined,
  progress: undefined
});

// Current active tab
export const activeTabStore = writable<TabId>('discover');

// Derived stores
export const isModalOpen = derived(modalStore, $modal => $modal.isOpen);
export const currentModal = derived(modalStore, $modal => $modal);
export const isLoading = derived(loadingStore, $loading => $loading.isLoading);
export const loadingMessage = derived(loadingStore, $loading => $loading.message);
export const activeTab = derived(activeTabStore, $tab => $tab);

// Actions
export const uiActions = {
  
  // Modal actions
  modal: {
    open(type: any, data?: any, onClose?: () => void, onConfirm?: (data?: any) => void) {
      modalStore.set({
        isOpen: true,
        type,
        data,
        onClose,
        onConfirm
      });
    },

    close() {
      modalStore.update(state => {
        if (state.onClose) {
          state.onClose();
        }
        return initialModalState;
      });
    },

    confirm(data?: any) {
      modalStore.update(state => {
        if (state.onConfirm) {
          state.onConfirm(data);
        }
        return initialModalState;
      });
    }
  },

  // Toast actions
  toast: {
    show(message: Omit<ToastMessage, 'id'>) {
      const id = Date.now().toString();
      const toast: ToastMessage = { id, ...message };
      
      toastStore.update(toasts => [...toasts, toast]);
      
      // Auto remove after duration
      const duration = message.duration || 3000;
      setTimeout(() => {
        this.remove(id);
      }, duration);
      
      return id;
    },

    success(message: string, title?: string) {
      return this.show({
        type: 'success',
        title,
        message
      });
    },

    error(message: string, title?: string) {
      return this.show({
        type: 'error',
        title,
        message,
        duration: 5000 // Error messages stay longer
      });
    },

    warning(message: string, title?: string) {
      return this.show({
        type: 'warning',
        title,
        message
      });
    },

    info(message: string, title?: string) {
      return this.show({
        type: 'info',
        title,
        message
      });
    },

    remove(id: string) {
      toastStore.update(toasts => toasts.filter(toast => toast.id !== id));
    },

    clear() {
      toastStore.set([]);
    }
  },

  // Loading actions
  loading: {
    start(message?: string, progress?: number) {
      loadingStore.set({
        isLoading: true,
        message,
        progress
      });
    },

    updateProgress(progress: number, message?: string) {
      loadingStore.update(state => ({
        ...state,
        progress,
        message: message || state.message
      }));
    },

    updateMessage(message: string) {
      loadingStore.update(state => ({
        ...state,
        message
      }));
    },

    stop() {
      loadingStore.set({
        isLoading: false,
        message: undefined,
        progress: undefined
      });
    }
  },

  // Tab actions
  tab: {
    setActive(tabId: TabId) {
      activeTabStore.set(tabId);
    }
  }
};
