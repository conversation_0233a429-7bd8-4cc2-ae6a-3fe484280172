#!/usr/bin/env tsx

import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from '../src/lib/server/db/schema';

// 从环境变量获取数据库连接
const DATABASE_URL = process.env.DATABASE_URL;

if (!DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is not set');
  console.log('Please set DATABASE_URL in your .env file');
  console.log('Example: DATABASE_URL="postgresql://username:password@localhost:5432/database"');
  process.exit(1);
}

async function initDatabase() {
  console.log('🚀 Initializing database...');
  console.log('📍 Database URL:', DATABASE_URL.replace(/:[^:@]*@/, ':***@'));

  const client = postgres(DATABASE_URL);
  const db = drizzle(client, { schema });

  try {
    // 测试连接
    console.log('🔍 Testing database connection...');
    const result = await client`SELECT current_database(), version()`;
    console.log('✅ Connected to database:', result[0].current_database);
    console.log('📊 PostgreSQL version:', result[0].version.split(' ')[0]);

    // 检查表是否存在
    console.log('🔍 Checking if tables exist...');
    const tablesResult = await client`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('users', 'matches', 'point_transactions')
    `;

    const existingTables = tablesResult.map(row => row.table_name);
    console.log('📋 Existing tables:', existingTables);

    if (existingTables.length === 0) {
      console.log('⚠️  No tables found. You need to run database migrations.');
      console.log('Run: pnpm db:push');
    } else {
      // 检查用户数量
      const userCount = await db.select().from(schema.users);
      console.log('👥 Current user count:', userCount.length);

      if (userCount.length === 0) {
        console.log('💡 Database is ready but empty. Users will be created when they first log in.');
      } else {
        console.log('✅ Database is ready with existing users.');
      }
    }

  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    if (error instanceof Error) {
      if (error.message.includes('ECONNREFUSED')) {
        console.log('💡 Make sure PostgreSQL is running and accessible.');
        console.log('💡 If using Docker: docker compose up -d');
      } else if (error.message.includes('authentication failed')) {
        console.log('💡 Check your database credentials in .env file.');
      } else if (error.message.includes('database') && error.message.includes('does not exist')) {
        console.log('💡 The database does not exist. Create it first.');
      }
    }
    process.exit(1);
  } finally {
    await client.end();
  }

  console.log('🎉 Database initialization completed!');
}

// 运行初始化
initDatabase().catch(console.error);
