import { fail } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { basicProfileSchema } from '$lib/schemas/user';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async () => {
	return {
		form: await superValidate(zod(basicProfileSchema))
	};
};

export const actions: Actions = {
	default: async (event) => {
		const form = await superValidate(event, zod(basicProfileSchema));

		if (!form.valid) {
			return fail(400, {
				form
			});
		}

		try {
			// 这里处理保存用户基础资料的逻辑
			// const userId = event.locals.user?.id;
			// await updateUserProfile(userId, form.data);

			console.log('Basic profile data:', form.data);

			// 重定向到下一步或主页面
			// throw redirect(302, '/discover');

			return {
				form
			};
		} catch (error) {
			console.error('Failed to save basic profile:', error);
			return fail(500, {
				form,
				message: '保存失败，请重试'
			});
		}
	}
};
