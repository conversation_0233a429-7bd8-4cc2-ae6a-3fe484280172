import adapter from '@sveltejs/adapter-node';
import { vitePreprocess } from '@sveltejs/vite-plugin-svelte';
// 判断当前是否为生产环境
const isProduction = process.env.NODE_ENV === 'production';

const config = {
	preprocess: vitePreprocess(),
	kit: { adapter: adapter() },
	compilerOptions: {
		// `dev` 选项是 Svelte 5 中控制所有开发时功能的总开关。
		// 当它为 true 时，编译器会添加额外的代码，
		// 以便在运行时进行检查并为调试工具提供信息。
		// 我们设置它仅在非生产环境时为 true。
		dev: !isProduction
	}
};

export default config;
