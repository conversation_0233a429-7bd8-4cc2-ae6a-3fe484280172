<script lang="ts">
	import { onMount } from 'svelte';
	import { authActions, authStore, isAuthenticated } from '$lib/stores/auth';
	import { telegramWebApp } from '$lib/telegram';
	import { getTelegramDebugInfo } from '$lib/utils/debug';

	let { children } = $props();
	let isInitializing = $state(true);
	let error = $state<string | null>(null);
	let debugInfo = $state<any>(null);

	// 监听认证状态
	let auth = $authStore;
	let authenticated = $isAuthenticated;

	onMount(async () => {
		console.log('🚀 AuthGuard 开始初始化...');

		try {
			// 开发环境下获取调试信息
			if (
				typeof window !== 'undefined' &&
				(window.location.hostname === 'localhost' ||
					window.location.hostname === '127.0.0.1' ||
					window.location.hostname === '*************')
			) {
				debugInfo = getTelegramDebugInfo();
				console.log('🔍 调试信息:', debugInfo);
			}

			// 检查Telegram环境
			if (!telegramWebApp.isReady()) {
				console.warn('⚠️ Telegram WebApp 未准备就绪');
				// 在开发环境下允许继续，生产环境下显示错误
				if (
					typeof window !== 'undefined' &&
					(window.location.hostname === 'localhost' ||
						window.location.hostname === '127.0.0.1' ||
						window.location.hostname === '*************')
				) {
					console.log('🔧 开发环境：继续初始化认证');
				} else {
					error = '请在 Telegram 中打开此应用';
					isInitializing = false;
					return;
				}
			}

			// 先检查现有会话
			const initResult = await authActions.init();
			if (initResult.hasValidSession) {
				console.log('✅ 使用现有会话');
				return;
			}

			// 获取Telegram数据并登录
			const { getTelegramDataFromAnySource, validateTelegramData } = await import(
				'$lib/utils/telegram-data-parser'
			);
			const telegramData = getTelegramDataFromAnySource();

			if (!telegramData || !validateTelegramData(telegramData)) {
				throw new Error('无法获取有效的Telegram用户数据');
			}

			console.log('✅ 获取到Telegram用户数据:', telegramData.user);

			// 使用Telegram数据登录
			const result = await authActions.loginWithTelegram(
				telegramData.user,
				telegramData.start_param
			);

			if (!result.success) {
				error = result.error || '认证失败';
				console.error('❌ 认证失败:', error);
			} else {
				console.log('✅ 认证成功');
			}
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : '初始化失败';
			error = errorMessage;
			console.error('❌ AuthGuard 初始化失败:', err);
		} finally {
			isInitializing = false;
		}
	});

	function retry() {
		error = null;
		isInitializing = true;
		// 重新执行初始化
		setTimeout(async () => {
			try {
				// 重新执行认证流程
				const initResult = await authActions.init();
				if (initResult.hasValidSession) {
					error = null;
					return;
				}

				const { getTelegramDataFromAnySource, validateTelegramData } = await import(
					'$lib/utils/telegram-data-parser'
				);
				const telegramData = getTelegramDataFromAnySource();

				if (!telegramData || !validateTelegramData(telegramData)) {
					throw new Error('无法获取有效的Telegram用户数据');
				}

				const result = await authActions.loginWithTelegram(
					telegramData.user,
					telegramData.start_param
				);
				if (!result.success) {
					error = result.error || '认证失败';
				} else {
					error = null;
				}
			} catch (err) {
				error = err instanceof Error ? err.message : '重试失败';
			} finally {
				isInitializing = false;
			}
		}, 100);
	}

	async function injectDebugData() {
		if (typeof window !== 'undefined') {
			try {
				const { injectTelegramData } = await import('$lib/utils/telegram-data-parser');
				const sampleData =
					'user=%7B%22id%22%3A6662549305%2C%22first_name%22%3A%22Sher%22%2C%22last_name%22%3A%22Locked%22%2C%22language_code%22%3A%22zh-hans%22%2C%22allows_write_to_pm%22%3Atrue%2C%22photo_url%22%3A%22https%3A%5C%2F%5C%2Ft.me%5C%2Fi%5C%2Fuserpic%5C%2F320%5C%2FDdepy63fuyaEFYJihlJAK7hLpZg8faemv1v9KHbOpG0qfZbxe2eWH3aZRRmhCDFi.svg%22%7D&chat_instance=6891755669185758424&chat_type=private&auth_date=1750668363&signature=Rj5w3NzMUu93NJ6CCqZWoQ45WdNY9eqYcastZ_JEjKheFVDZpfimbaqnPVUONCP1oOxGyMKXBoG0HrsvsHphDA&hash=81ac3f3d0dc817c6b3a87b31505be0867a27b9f9f6e67b9bd64b461dbcfee434';

				if (injectTelegramData(sampleData)) {
					alert('✅ 调试数据已注入！点击重试。');
				} else {
					alert('❌ 数据注入失败');
				}
			} catch (error) {
				console.error('导入失败:', error);
				alert('❌ 导入模块失败');
			}
		}
	}
</script>

{#if isInitializing || auth.isLoading}
	<!-- 加载状态 -->
	<div
		class="flex min-h-screen items-center justify-center bg-gradient-to-br from-blue-500 to-purple-600"
	>
		<div class="mx-4 w-full max-w-md rounded-lg bg-white p-8 shadow-xl">
			<div class="text-center">
				<div class="mb-6">
					<div class="mb-2 text-4xl">🔥</div>
					<h1 class="text-2xl font-bold text-gray-900">BlueX</h1>
					<p class="mt-2 text-gray-600">发现志同道合的朋友</p>
				</div>

				<div class="space-y-4">
					<div class="mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-blue-500"></div>
					<p class="text-gray-600">正在认证...</p>
				</div>
			</div>
		</div>
	</div>
{:else if error || !authenticated}
	<!-- 错误状态 -->
	<div
		class="flex min-h-screen items-center justify-center bg-gradient-to-br from-blue-500 to-purple-600"
	>
		<div class="mx-4 w-full max-w-md rounded-lg bg-white p-8 shadow-xl">
			<div class="text-center">
				<div class="mb-6">
					<div class="mb-2 text-4xl">🔥</div>
					<h1 class="text-2xl font-bold text-gray-900">BlueX</h1>
					<p class="mt-2 text-gray-600">发现志同道合的朋友</p>
				</div>

				<div class="space-y-4">
					<div class="rounded-lg bg-red-50 p-3 text-sm text-red-500">
						{error || '认证失败，请重试'}
					</div>

					<!-- 开发环境调试信息 -->
					{#if debugInfo && (debugInfo.hostname === 'localhost' || debugInfo.hostname === '127.0.0.1')}
						<details class="text-left">
							<summary class="cursor-pointer text-sm text-gray-600">🔍 调试信息</summary>
							<div class="mt-2 rounded bg-gray-100 p-2 text-xs">
								<p><strong>环境:</strong> {debugInfo.hostname}</p>
								<p><strong>Telegram WebApp:</strong> {debugInfo.isTelegramWebApp ? '✅' : '❌'}</p>
								<p><strong>WebApp 数据:</strong> {debugInfo.hasWebAppData ? '✅' : '❌'}</p>
								<p><strong>Session 数据:</strong> {debugInfo.hasSessionData ? '✅' : '❌'}</p>
								<p><strong>URL 参数:</strong> {debugInfo.hasUrlParams ? '✅' : '❌'}</p>
								{#if debugInfo.telegramData}
									<p><strong>数据源:</strong> {debugInfo.telegramData.source}</p>
									<p>
										<strong>用户:</strong>
										{debugInfo.telegramData.user?.first_name} ({debugInfo.telegramData.user?.id})
									</p>
								{/if}
								{#if debugInfo.errors.length > 0}
									<p><strong>错误:</strong></p>
									<ul class="list-inside list-disc">
										{#each debugInfo.errors as err}
											<li class="text-red-600">{err}</li>
										{/each}
									</ul>
								{/if}
							</div>
						</details>

						<div class="flex gap-2">
							<button
								onclick={injectDebugData}
								class="flex-1 rounded-lg bg-gray-500 px-4 py-2 text-white transition-colors hover:bg-gray-600"
							>
								注入调试数据
							</button>
							<button
								onclick={retry}
								class="flex-1 rounded-lg bg-blue-500 px-4 py-2 text-white transition-colors hover:bg-blue-600"
							>
								重试
							</button>
						</div>
					{:else}
						<button
							onclick={retry}
							class="w-full rounded-lg bg-blue-500 px-4 py-2 text-white transition-colors hover:bg-blue-600"
						>
							重试
						</button>
					{/if}
				</div>
			</div>
		</div>
	</div>
{:else}
	<!-- 认证成功，显示应用内容 -->
	{@render children()}
{/if}
