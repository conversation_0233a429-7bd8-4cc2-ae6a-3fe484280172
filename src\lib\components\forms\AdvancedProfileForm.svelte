<script lang="ts">
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { kinkPreferencesSchema, privacySettingsSchema } from '$lib/schemas/user';
	import { KINK_CATEGORIES } from '$lib/utils/constants';
	import { Button } from '$lib/components/ui/button';
	import { Label } from '$lib/components/ui/label';
	import { Heart, Shield, Sliders } from '@lucide/svelte';
	import { z } from 'zod';

	// 合并 schema
	const advancedSchema = kinkPreferencesSchema.merge(privacySettingsSchema);
	type AdvancedProfile = z.infer<typeof advancedSchema>;

	interface Props {
		data: any;
		onSubmit?: (data: AdvancedProfile) => void;
		onCancel?: () => void;
		isLoading?: boolean;
	}

	let { data, onSubmit, onCancel, isLoading = false }: Props = $props();

	const { form, errors, enhance, submitting } = superForm(data, {
		validators: zodClient(advancedSchema),
		onUpdated: ({ form }) => {
			if (form.valid && onSubmit) {
				onSubmit(form.data);
			}
		}
	});

	// Kink 评分状态
	let kinkRatings = $state<Record<string, number>>({});
	let selectedCategories = $state<number[]>([]);

	// 示例 Kink 项目（实际应该从 API 获取）
	const sampleKinks = [
		{ code: 'bondage', name: '束缚', category: 100 },
		{ code: 'roleplay', name: '角色扮演', category: 200 },
		{ code: 'dominance', name: '支配', category: 100 },
		{ code: 'submission', name: '服从', category: 100 },
		{ code: 'fetish', name: '恋物', category: 300 }
	];

	function updateKinkRating(kinkCode: string, rating: number) {
		kinkRatings[kinkCode] = rating;
		$form.kinkRatings = { ...kinkRatings };
	}

	function getRatingLabel(rating: number) {
		const labels = new Map<number, string>([
			[-1, '拒绝'],
			[0, '中立'],
			[1, '略感兴趣'],
			[2, '感兴趣'],
			[3, '很感兴趣'],
			[4, '非常感兴趣'],
			[5, '极度喜欢']
		]);

		// 使用 .get() 方法来安全地获取值
		return labels.get(rating) || '未知';
	}

	function getRatingColor(rating: number) {
		if (rating === -1) return 'bg-red-500';
		if (rating === 0) return 'bg-gray-400';
		if (rating <= 2) return 'bg-yellow-500';
		if (rating <= 4) return 'bg-green-500';
		return 'bg-purple-500';
	}
</script>

<div class="mx-auto max-w-2xl rounded-lg bg-white p-6 shadow-lg dark:bg-gray-800">
	<div class="mb-6 text-center">
		<div
			class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-purple-100 dark:bg-purple-900"
		>
			<Heart class="h-8 w-8 text-purple-600 dark:text-purple-400" />
		</div>
		<h2 class="mb-2 text-2xl font-bold text-gray-900 dark:text-white">高级偏好设置</h2>
		<p class="text-sm text-gray-600 dark:text-gray-300">设置您的详细偏好以获得更精准的匹配</p>
	</div>

	<form method="POST" use:enhance class="space-y-8">
		<!-- Kink 偏好设置 -->
		<div class="space-y-4">
			<div class="flex items-center space-x-2">
				<Sliders class="h-5 w-5 text-purple-600 dark:text-purple-400" />
				<h3 class="text-lg font-semibold text-gray-900 dark:text-white">兴趣偏好评分</h3>
			</div>

			<p class="text-sm text-gray-600 dark:text-gray-300">
				请为以下项目评分（-1=拒绝，0=中立，5=极度喜欢）
			</p>

			<div class="space-y-6">
				{#each sampleKinks as kink}
					<div class="space-y-3">
						<Label class="text-base font-medium">
							{kink.name}
						</Label>

						<div class="flex items-center space-x-2">
							{#each [-1, 0, 1, 2, 3, 4, 5] as rating}
								<button
									type="button"
									onclick={() => updateKinkRating(kink.code, rating)}
									class="h-8 w-8 rounded-full border-2 transition-all {kinkRatings[kink.code] ===
									rating
										? `${getRatingColor(rating)} border-gray-800 text-white dark:border-white`
										: 'border-gray-300 hover:border-gray-400 dark:border-gray-600 dark:hover:border-gray-500'}"
									title={getRatingLabel(rating)}
								>
									{rating === -1 ? '✕' : rating}
								</button>
							{/each}
						</div>

						{#if kinkRatings[kink.code] !== undefined}
							<p class="text-sm text-gray-600 dark:text-gray-300">
								当前评分: {getRatingLabel(kinkRatings[kink.code])}
							</p>
						{/if}
					</div>
				{/each}
			</div>
		</div>

		<!-- 隐私设置 -->
		<div class="space-y-4">
			<div class="flex items-center space-x-2">
				<Shield class="h-5 w-5 text-blue-600 dark:text-blue-400" />
				<h3 class="text-lg font-semibold text-gray-900 dark:text-white">隐私设置</h3>
			</div>

			<div class="rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20">
				<h4 class="mb-2 font-medium text-blue-900 dark:text-blue-100">屏蔽设置</h4>
				<p class="text-sm text-blue-700 dark:text-blue-300">
					选择您不希望被哪些类型的用户看到您的资料
				</p>

				<!-- 这里可以添加具体的隐私设置选项 -->
				<div class="mt-4 space-y-2">
					<label class="flex items-center space-x-2">
						<input type="checkbox" class="rounded" />
						<span class="text-sm">不被未验证用户看到</span>
					</label>
					<label class="flex items-center space-x-2">
						<input type="checkbox" class="rounded" />
						<span class="text-sm">不被新注册用户看到</span>
					</label>
				</div>
			</div>
		</div>

		<!-- 重要提醒 -->
		<div class="rounded-lg bg-yellow-50 p-4 dark:bg-yellow-900/20">
			<div class="flex items-start space-x-2">
				<div class="text-lg text-yellow-500">⚠️</div>
				<div>
					<h4 class="text-sm font-medium text-yellow-900 dark:text-yellow-100">重要提醒</h4>
					<p class="mt-1 text-xs text-yellow-700 dark:text-yellow-300">
						这些设置将影响您的匹配结果。您可以随时在设置中修改这些偏好。
					</p>
				</div>
			</div>
		</div>

		<!-- 操作按钮 -->
		<div class="flex space-x-3 pt-4">
			{#if onCancel}
				<Button
					type="button"
					variant="outline"
					class="flex-1"
					onclick={onCancel}
					disabled={$submitting || isLoading}
				>
					稍后设置
				</Button>
			{/if}

			<Button type="submit" class="flex-1" disabled={$submitting || isLoading}>
				{#if $submitting || isLoading}
					<div
						class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"
					></div>
					保存中...
				{:else}
					完成设置
				{/if}
			</Button>
		</div>
	</form>
</div>
