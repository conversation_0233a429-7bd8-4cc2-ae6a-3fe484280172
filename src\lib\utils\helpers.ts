// Helper utilities

import type { UserProfile, UserRegistrationStatus, ProfileCompletionStep } from '../types';
import { APP_CONFIG } from './constants';

export function generateKinkMapCode(): string {
	// 生成 8 位随机字符串作为 Kink Map 分享码
	const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
	let result = '';
	for (let i = 0; i < 8; i++) {
		result += chars.charAt(Math.floor(Math.random() * chars.length));
	}
	return result;
}

export function calculateProfileCompleteness(user: Partial<UserProfile>): number {
	const weights = {
		nickname: 15,
		age: 15,
		orientation: 10,
		bodyType: 10,
		presentationStyle: 10,
		bio: 10,
		country: 5,
		city: 5,
		profileImageUrl: 10,
		kinkRatings: 10
	};

	let score = 0;
	let maxScore = 0;

	for (const [field, weight] of Object.entries(weights)) {
		maxScore += weight;
		if (user[field as keyof UserProfile]) {
			if (field === 'kinkRatings') {
				// 检查是否有至少 3 个 kink 评分
				const ratings = (user.kinkRatings as Record<string, number>) || {};
				if (Object.keys(ratings).length >= 3) {
					score += weight;
				}
			} else {
				score += weight;
			}
		}
	}

	return Math.round((score / maxScore) * 100);
}

export function getUserRegistrationStatus(user: Partial<UserProfile>): UserRegistrationStatus {
	if (!user.nickname || !user.age || !user.orientation) {
		return 'new_user';
	}

	const completeness = calculateProfileCompleteness(user);

	if (completeness >= 80) {
		return 'fully_complete';
	} else if (completeness >= 60 && user.kinkRatings && Object.keys(user.kinkRatings).length >= 3) {
		return 'advanced_complete';
	} else if (completeness >= 40) {
		return 'basic_complete';
	} else {
		return 'new_user';
	}
}

export function getNextProfileStep(user: Partial<UserProfile>): ProfileCompletionStep | undefined {
	const steps: ProfileCompletionStep[] = [
		'nickname',
		'age',
		'orientation',
		'body_type',
		'presentation_style',
		'location',
		'bio',
		'kink_preferences',
		'privacy_settings'
	];

	for (const step of steps) {
		switch (step) {
			case 'nickname':
				if (!user.nickname) return step;
				break;
			case 'age':
				if (!user.age) return step;
				break;
			case 'orientation':
				if (!user.orientation) return step;
				break;
			case 'body_type':
				if (!user.bodyType) return step;
				break;
			case 'presentation_style':
				if (!user.presentationStyle) return step;
				break;
			case 'location':
				if (!user.country) return step;
				break;
			case 'bio':
				if (!user.bio) return step;
				break;
			case 'kink_preferences':
				if (!user.kinkRatings || Object.keys(user.kinkRatings).length < 3) return step;
				break;
			case 'privacy_settings':
				if (!user.blockVisibilityFromBodyTypes) return step;
				break;
		}
	}

	return undefined;
}
export function checkCanUseAdvancedSearch(user: Partial<UserProfile>): boolean {
	const status = getUserRegistrationStatus(user);
	return status === 'advanced_complete' || status === 'fully_complete';
}

/**
 * 检查用户是否能使用超级搜索
 * @param user 用户部分资料
 * @returns boolean
 */
export function checkCanUseSuperSearch(user: Partial<UserProfile>): boolean {
	const status = getUserRegistrationStatus(user);
	if (status !== 'advanced_complete' && status !== 'fully_complete') {
		return false;
	}
	return (user.vipLevel ?? 0) > 0;
}

export function calculateSearchCost(searchType: 'basic' | 'advanced' | 'super'): number {
	switch (searchType) {
		case 'basic':
			return APP_CONFIG.points.search_cost;
		case 'advanced':
			return APP_CONFIG.points.search_cost * 2;
		case 'super':
			return APP_CONFIG.points.super_search_cost;
		default:
			return APP_CONFIG.points.search_cost;
	}
}

export function hasEnoughPoints(user: Partial<UserProfile>, cost: number): boolean {
	return (user.pointBalance || 0) >= cost;
}

export function debounce<T extends (...args: any[]) => any>(
	func: T,
	wait: number
): (...args: Parameters<T>) => void {
	let timeout: NodeJS.Timeout;
	return (...args: Parameters<T>) => {
		clearTimeout(timeout);
		timeout = setTimeout(() => func(...args), wait);
	};
}

export function throttle<T extends (...args: any[]) => any>(
	func: T,
	limit: number
): (...args: Parameters<T>) => void {
	let inThrottle: boolean;
	return (...args: Parameters<T>) => {
		if (!inThrottle) {
			func(...args);
			inThrottle = true;
			setTimeout(() => (inThrottle = false), limit);
		}
	};
}

export function sleep(ms: number): Promise<void> {
	return new Promise((resolve) => setTimeout(resolve, ms));
}

export function randomBetween(min: number, max: number): number {
	return Math.floor(Math.random() * (max - min + 1)) + min;
}
