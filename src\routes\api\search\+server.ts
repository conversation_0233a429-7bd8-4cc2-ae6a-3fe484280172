import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { searchOptionsSchema } from '$lib/schemas/search';

// 模拟用户数据（实际应该从数据库获取）
const mockUsers = [
	{
		id: '1',
		nickname: '<PERSON>',
		age: 25,
		orientation: 'bisexual',
		bodyType: 'female_body',
		presentationStyle: 'feminine',
		bio: '喜欢探索新事物，寻找志同道合的朋友',
		city: '北京',
		country: '中国',
		profileImageUrl: null,
		isVerified: true,
		trustScore: 85,
		vipLevel: 1,
		lastActiveAt: new Date(),
		profileCompletenessScore: 90,
		kinkRatings: { bondage: 3, roleplay: 4 }
	},
	{
		id: '2',
		nickname: '<PERSON>',
		age: 28,
		orientation: 'straight',
		bodyType: 'male_body',
		presentationStyle: 'conventional_masculine',
		bio: '热爱生活，寻找真诚的连接',
		city: '上海',
		country: '中国',
		profileImageUrl: null,
		isVerified: false,
		trustScore: 72,
		vipLevel: 0,
		lastActiveAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
		profileCompletenessScore: 75,
		kinkRatings: { dominance: 4, fetish: 2 }
	},
	{
		id: '3',
		nickname: 'Charlie',
		age: 30,
		orientation: 'gay',
		bodyType: 'male_body',
		presentationStyle: 'rugged_masculine',
		bio: '户外爱好者，喜欢冒险',
		city: '深圳',
		country: '中国',
		profileImageUrl: null,
		isVerified: true,
		trustScore: 95,
		vipLevel: 2,
		lastActiveAt: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
		profileCompletenessScore: 95,
		kinkRatings: { submission: 5, roleplay: 3 }
	}
];

export const POST: RequestHandler = async ({ request }) => {
	try {
		const body = await request.json();

		// 验证请求数据
		const validation = searchOptionsSchema.safeParse(body);
		if (!validation.success) {
			return json(
				{ error: 'Invalid search parameters', details: validation.error.errors },
				{ status: 400 }
			);
		}

		const { mode, filters, sortBy = 'relevance', limit = 20, offset = 0 } = validation.data;

		// 模拟搜索逻辑
		let filteredUsers = [...mockUsers];

		// 应用过滤器
		if (filters.ageRange) {
			const [minAge, maxAge] = filters.ageRange;
			filteredUsers = filteredUsers.filter((user) => user.age >= minAge && user.age <= maxAge);
		}

		if (filters.orientation && filters.orientation.length > 0) {
			filteredUsers = filteredUsers.filter((user) =>
				filters.orientation!.includes(user.orientation as any)
			);
		}

		if (filters.bodyType && filters.bodyType.length > 0) {
			filteredUsers = filteredUsers.filter((user) =>
				filters.bodyType!.includes(user.bodyType as any)
			);
		}

		if (filters.presentationStyle && filters.presentationStyle.length > 0) {
			filteredUsers = filteredUsers.filter((user) =>
				filters.presentationStyle!.includes(user.presentationStyle as any)
			);
		}

		// 计算匹配分数（简化版本）
		const results = filteredUsers.map((user) => {
			let matchScore = 50; // 基础分数

			// 根据搜索模式调整分数计算
			if (mode === 'advanced' || mode === 'super') {
				// 基于 kink 偏好计算匹配度
				const userKinks = Object.keys(user.kinkRatings);
				if (userKinks.length > 0) {
					matchScore += userKinks.length * 10; // 简化计算
				}
			}

			// 基于资料完整度调整
			matchScore += user.profileCompletenessScore * 0.3;

			// 基于信誉分调整
			matchScore += user.trustScore * 0.2;

			// 确保分数在 0-100 范围内
			matchScore = Math.min(100, Math.max(0, Math.round(matchScore)));

			return {
				user,
				matchScore,
				distance: Math.random() * 50, // 模拟距离
				commonKinks: mode !== 'basic' ? ['roleplay', 'bondage'] : undefined,
				isOnline: Math.random() > 0.5
			};
		});

		// 排序
		if (sortBy === 'relevance') {
			results.sort((a, b) => b.matchScore! - a.matchScore!);
		} else if (sortBy === 'distance') {
			results.sort((a, b) => a.distance! - b.distance!);
		} else if (sortBy === 'lastActive') {
			results.sort(
				(a, b) => new Date(b.user.lastActiveAt).getTime() - new Date(a.user.lastActiveAt).getTime()
			);
		} else if (sortBy === 'trustScore') {
			results.sort((a, b) => b.user.trustScore - a.user.trustScore);
		}

		// 分页
		const paginatedResults = results.slice(offset, offset + limit);
		const hasMore = results.length > offset + limit;

		return json({
			results: paginatedResults,
			total: results.length,
			hasMore,
			searchId: `search_${Date.now()}`
		});
	} catch (error) {
		console.error('Search API error:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};
