var Vt=Object.create;var It=Object.defineProperty;var Ht=Object.getOwnPropertyDescriptor;var Xt=Object.getOwnPropertyNames;var Yt=Object.getPrototypeOf,tn=Object.prototype.hasOwnProperty;var nn=(l,c)=>()=>(c||l((c={exports:{}}).exports,c),c.exports);var rn=(l,c,p,u)=>{if(c&&typeof c=="object"||typeof c=="function")for(let f of Xt(c))!tn.call(l,f)&&f!==p&&It(l,f,{get:()=>c[f],enumerable:!(u=Ht(c,f))||u.enumerable});return l};var en=(l,c,p)=>(p=l!=null?Vt(Yt(l)):{},rn(c||!l||!l.__esModule?It(p,"default",{value:l,enumerable:!0}):p,l));var Lt=nn((J,gt)=>{(function(l,c){typeof J=="object"&&typeof gt=="object"?gt.exports=c():typeof define=="function"&&define.amd?define([],c):typeof J=="object"?J.Parsimmon=c():l.Parsimmon=c()})(typeof self<"u"?self:J,function(){return function(l){var c={};function p(u){if(c[u])return c[u].exports;var f=c[u]={i:u,l:!1,exports:{}};return l[u].call(f.exports,f,f.exports,p),f.l=!0,f.exports}return p.m=l,p.c=c,p.d=function(u,f,Z){p.o(u,f)||Object.defineProperty(u,f,{configurable:!1,enumerable:!0,get:Z})},p.r=function(u){Object.defineProperty(u,"__esModule",{value:!0})},p.n=function(u){var f=u&&u.__esModule?function(){return u.default}:function(){return u};return p.d(f,"a",f),f},p.o=function(u,f){return Object.prototype.hasOwnProperty.call(u,f)},p.p="",p(p.s=0)}([function(l,c,p){"use strict";function u(t){if(!(this instanceof u))return new u(t);this._=t}var f=u.prototype;function Z(t,n){for(var r=0;r<t;r++)n(r)}function I(t,n,r){return function(e,o){Z(o.length,function(i){e(o[i],i,o)})}(function(e,o,i){n=t(n,e,o,i)},r),n}function j(t,n){return I(function(r,e,o,i){return r.concat([t(e,o,i)])},[],n)}function At(t,n){var r={v:0,buf:n};return Z(t,function(){var e;r={v:r.v<<1|(e=r.buf,e[0]>>7),buf:function(o){var i=I(function(a,s,d,y){return a.concat(d===y.length-1?Buffer.from([s,0]).readUInt16BE(0):y.readUInt16BE(d))},[],o);return Buffer.from(j(function(a){return(a<<1&65535)>>8},i))}(r.buf)}}),r}function dt(){return typeof Buffer<"u"}function C(){if(!dt())throw new Error("Buffer global does not exist; please use webpack if you need to parse Buffers in the browser.")}function ht(t){C();var n=I(function(i,a){return i+a},0,t);if(n%8!=0)throw new Error("The bits ["+t.join(", ")+"] add up to "+n+" which is not an even number of bytes; the total should be divisible by 8");var r,e=n/8,o=(r=function(i){return i>48},I(function(i,a){return i||(r(a)?a:i)},null,t));if(o)throw new Error(o+" bit range requested exceeds 48 bit (6 byte) Number max.");return new u(function(i,a){var s=e+a;return s>i.length?b(a,e.toString()+" bytes"):h(s,I(function(d,y){var v=At(y,d.buf);return{coll:d.coll.concat(v.v),buf:v.buf}},{coll:[],buf:i.slice(a,s)},t).coll)})}function E(t,n){return new u(function(r,e){return C(),e+n>r.length?b(e,n+" bytes for "+t):h(e+n,r.slice(e,e+n))})}function K(t,n){if(typeof(r=n)!="number"||Math.floor(r)!==r||n<0||n>6)throw new Error(t+" requires integer length in range [0, 6].");var r}function V(t){return K("uintBE",t),E("uintBE("+t+")",t).map(function(n){return n.readUIntBE(0,t)})}function H(t){return K("uintLE",t),E("uintLE("+t+")",t).map(function(n){return n.readUIntLE(0,t)})}function X(t){return K("intBE",t),E("intBE("+t+")",t).map(function(n){return n.readIntBE(0,t)})}function Y(t){return K("intLE",t),E("intLE("+t+")",t).map(function(n){return n.readIntLE(0,t)})}function U(t){return t instanceof u}function q(t){return{}.toString.call(t)==="[object Array]"}function W(t){return dt()&&Buffer.isBuffer(t)}function h(t,n){return{status:!0,index:t,value:n,furthest:-1,expected:[]}}function b(t,n){return q(n)||(n=[n]),{status:!1,index:-1,value:null,furthest:t,expected:n}}function w(t,n){if(!n||t.furthest>n.furthest)return t;var r=t.furthest===n.furthest?function(e,o){if(function(){if(u._supportsSet!==void 0)return u._supportsSet;var S=typeof Set<"u";return u._supportsSet=S,S}()&&Array.from){for(var i=new Set(e),a=0;a<o.length;a++)i.add(o[a]);var s=Array.from(i);return s.sort(),s}for(var d={},y=0;y<e.length;y++)d[e[y]]=!0;for(var v=0;v<o.length;v++)d[o[v]]=!0;var _=[];for(var x in d)({}).hasOwnProperty.call(d,x)&&_.push(x);return _.sort(),_}(t.expected,n.expected):n.expected;return{status:t.status,index:t.index,value:t.value,furthest:n.furthest,expected:r}}var tt={};function mt(t,n){if(W(t))return{offset:n,line:-1,column:-1};t in tt||(tt[t]={});for(var r=tt[t],e=0,o=0,i=0,a=n;a>=0;){if(a in r){e=r[a].line,i===0&&(i=r[a].lineStart);break}(t.charAt(a)===`
`||t.charAt(a)==="\r"&&t.charAt(a+1)!==`
`)&&(o++,i===0&&(i=a+1)),a--}var s=e+o,d=n-i;return r[n]={line:s,lineStart:i},{offset:n,line:s+1,column:d+1}}function A(t){if(!U(t))throw new Error("not a parser: "+t)}function nt(t,n){return typeof t=="string"?t.charAt(n):t[n]}function F(t){if(typeof t!="number")throw new Error("not a number: "+t)}function L(t){if(typeof t!="function")throw new Error("not a function: "+t)}function T(t){if(typeof t!="string")throw new Error("not a string: "+t)}var Ft=2,Nt=3,O=8,Rt=5*O,zt=4*O,vt="  ";function rt(t,n){return new Array(n+1).join(t)}function et(t,n,r){var e=n-t.length;return e<=0?t:rt(r,e)+t}function yt(t,n,r,e){return{from:t-n>0?t-n:0,to:t+r>e?e:t+r}}function Dt(t,n){var r,e,o,i,a,s=n.index,d=s.offset,y=1;if(d===t.length)return"Got the end of the input";if(W(t)){var v=d-d%O,_=d-v,x=yt(v,Rt,zt+O,t.length),S=j(function(m){return j(function(R){return et(R.toString(16),2,"0")},m)},function(m,R){var z=m.length,M=[],D=0;if(z<=R)return[m.slice()];for(var Q=0;Q<z;Q++)M[D]||M.push([]),M[D].push(m[Q]),(Q+1)%R==0&&D++;return M}(t.slice(x.from,x.to).toJSON().data,O));i=function(m){return m.from===0&&m.to===1?{from:m.from,to:m.to}:{from:m.from/O,to:Math.floor(m.to/O)}}(x),e=v/O,r=3*_,_>=4&&(r+=1),y=2,o=j(function(m){return m.length<=4?m.join(" "):m.slice(0,4).join(" ")+"  "+m.slice(4).join(" ")},S),(a=(8*(i.to>0?i.to-1:i.to)).toString(16).length)<2&&(a=2)}else{var N=t.split(/\r\n|[\n\r\u2028\u2029]/);r=s.column-1,e=s.line-1,i=yt(e,Ft,Nt,N.length),o=N.slice(i.from,i.to),a=i.to.toString().length}var Kt=e-i.from;return W(t)&&(a=(8*(i.to>0?i.to-1:i.to)).toString(16).length)<2&&(a=2),I(function(m,R,z){var M,D=z===Kt,Q=D?"> ":vt;return M=W(t)?et((8*(i.from+z)).toString(16),a,"0"):et((i.from+z+1).toString(),a," "),[].concat(m,[Q+M+" | "+R],D?[vt+rt(" ",a)+" | "+et("",r," ")+rt("^",y)]:[])},[],o).join(`
`)}function bt(t,n){return[`
`,"-- PARSING FAILED "+rt("-",50),`

`,Dt(t,n),`

`,(r=n.expected,r.length===1?`Expected:

`+r[0]:`Expected one of the following: 

`+r.join(", ")),`
`].join("");var r}function xt(t){return t.flags!==void 0?t.flags:[t.global?"g":"",t.ignoreCase?"i":"",t.multiline?"m":"",t.unicode?"u":"",t.sticky?"y":""].join("")}function ut(){for(var t=[].slice.call(arguments),n=t.length,r=0;r<n;r+=1)A(t[r]);return u(function(e,o){for(var i,a=new Array(n),s=0;s<n;s+=1){if(!(i=w(t[s]._(e,o),i)).status)return i;a[s]=i.value,o=i.index}return w(h(o,a),i)})}function k(){var t=[].slice.call(arguments);if(t.length===0)throw new Error("seqMap needs at least one argument");var n=t.pop();return L(n),ut.apply(null,t).map(function(r){return n.apply(null,r)})}function ot(){var t=[].slice.call(arguments),n=t.length;if(n===0)return it("zero alternates");for(var r=0;r<n;r+=1)A(t[r]);return u(function(e,o){for(var i,a=0;a<t.length;a+=1)if((i=w(t[a]._(e,o),i)).status)return i;return i})}function wt(t,n){return st(t,n).or(P([]))}function st(t,n){return A(t),A(n),k(t,n.then(t).many(),function(r,e){return[r].concat(e)})}function $(t){T(t);var n="'"+t+"'";return u(function(r,e){var o=e+t.length,i=r.slice(e,o);return i===t?h(o,i):b(e,n)})}function B(t,n){(function(o){if(!(o instanceof RegExp))throw new Error("not a regexp: "+o);for(var i=xt(o),a=0;a<i.length;a++){var s=i.charAt(a);if(s!=="i"&&s!=="m"&&s!=="u"&&s!=="s")throw new Error('unsupported regexp flag "'+s+'": '+o)}})(t),arguments.length>=2?F(n):n=0;var r=function(o){return RegExp("^(?:"+o.source+")",xt(o))}(t),e=""+t;return u(function(o,i){var a=r.exec(o.slice(i));if(a){if(0<=n&&n<=a.length){var s=a[0],d=a[n];return h(i+s.length,d)}return b(i,"valid match group (0 to "+a.length+") in "+e)}return b(i,e)})}function P(t){return u(function(n,r){return h(r,t)})}function it(t){return u(function(n,r){return b(r,t)})}function at(t){if(U(t))return u(function(n,r){var e=t._(n,r);return e.index=r,e.value="",e});if(typeof t=="string")return at($(t));if(t instanceof RegExp)return at(B(t));throw new Error("not a string, regexp, or parser: "+t)}function Et(t){return A(t),u(function(n,r){var e=t._(n,r),o=n.slice(r,e.index);return e.status?b(r,'not "'+o+'"'):h(r,null)})}function ft(t){return L(t),u(function(n,r){var e=nt(n,r);return r<n.length&&t(e)?h(r+1,e):b(r,"a character/byte matching "+t)})}function Bt(t,n){arguments.length<2&&(n=t,t=void 0);var r=u(function(e,o){return r._=n()._,r._(e,o)});return t?r.desc(t):r}function lt(){return it("fantasy-land/empty")}f.parse=function(t){if(typeof t!="string"&&!W(t))throw new Error(".parse must be called with a string or Buffer as its argument");var n,r=this.skip(pt)._(t,0);return n=r.status?{status:!0,value:r.value}:{status:!1,index:mt(t,r.furthest),expected:r.expected},delete tt[t],n},f.tryParse=function(t){var n=this.parse(t);if(n.status)return n.value;var r=bt(t,n),e=new Error(r);throw e.type="ParsimmonError",e.result=n,e},f.assert=function(t,n){return this.chain(function(r){return t(r)?P(r):it(n)})},f.or=function(t){return ot(this,t)},f.trim=function(t){return this.wrap(t,t)},f.wrap=function(t,n){return k(t,this,n,function(r,e){return e})},f.thru=function(t){return t(this)},f.then=function(t){return A(t),ut(this,t).map(function(n){return n[1]})},f.many=function(){var t=this;return u(function(n,r){for(var e=[],o=void 0;;){if(!(o=w(t._(n,r),o)).status)return w(h(r,e),o);if(r===o.index)throw new Error("infinite loop detected in .many() parser --- calling .many() on a parser which can accept zero characters is usually the cause");r=o.index,e.push(o.value)}})},f.tieWith=function(t){return T(t),this.map(function(n){if(function(o){if(!q(o))throw new Error("not an array: "+o)}(n),n.length){T(n[0]);for(var r=n[0],e=1;e<n.length;e++)T(n[e]),r+=t+n[e];return r}return""})},f.tie=function(){return this.tieWith("")},f.times=function(t,n){var r=this;return arguments.length<2&&(n=t),F(t),F(n),u(function(e,o){for(var i=[],a=void 0,s=void 0,d=0;d<t;d+=1){if(s=w(a=r._(e,o),s),!a.status)return s;o=a.index,i.push(a.value)}for(;d<n&&(s=w(a=r._(e,o),s),a.status);d+=1)o=a.index,i.push(a.value);return w(h(o,i),s)})},f.result=function(t){return this.map(function(){return t})},f.atMost=function(t){return this.times(0,t)},f.atLeast=function(t){return k(this.times(t),this.many(),function(n,r){return n.concat(r)})},f.map=function(t){L(t);var n=this;return u(function(r,e){var o=n._(r,e);return o.status?w(h(o.index,t(o.value)),o):o})},f.contramap=function(t){L(t);var n=this;return u(function(r,e){var o=n.parse(t(r.slice(e)));return o.status?h(e+r.length,o.value):o})},f.promap=function(t,n){return L(t),L(n),this.contramap(t).map(n)},f.skip=function(t){return ut(this,t).map(function(n){return n[0]})},f.mark=function(){return k(G,this,G,function(t,n,r){return{start:t,value:n,end:r}})},f.node=function(t){return k(G,this,G,function(n,r,e){return{name:t,value:r,start:n,end:e}})},f.sepBy=function(t){return wt(this,t)},f.sepBy1=function(t){return st(this,t)},f.lookahead=function(t){return this.skip(at(t))},f.notFollowedBy=function(t){return this.skip(Et(t))},f.desc=function(t){q(t)||(t=[t]);var n=this;return u(function(r,e){var o=n._(r,e);return o.status||(o.expected=t),o})},f.fallback=function(t){return this.or(P(t))},f.ap=function(t){return k(t,this,function(n,r){return n(r)})},f.chain=function(t){var n=this;return u(function(r,e){var o=n._(r,e);return o.status?w(t(o.value)._(r,o.index),o):o})},f.concat=f.or,f.empty=lt,f.of=P,f["fantasy-land/ap"]=f.ap,f["fantasy-land/chain"]=f.chain,f["fantasy-land/concat"]=f.concat,f["fantasy-land/empty"]=f.empty,f["fantasy-land/of"]=f.of,f["fantasy-land/map"]=f.map;var G=u(function(t,n){return h(n,mt(t,n))}),Qt=u(function(t,n){return n>=t.length?b(n,"any character/byte"):h(n+1,nt(t,n))}),Ut=u(function(t,n){return h(t.length,t.slice(n))}),pt=u(function(t,n){return n<t.length?b(n,"EOF"):h(n,null)}),Wt=B(/[0-9]/).desc("a digit"),Tt=B(/[0-9]*/).desc("optional digits"),$t=B(/[a-z]/i).desc("a letter"),Gt=B(/[a-z]*/i).desc("optional letters"),Jt=B(/\s*/).desc("optional whitespace"),Zt=B(/\s+/).desc("whitespace"),_t=$("\r"),St=$(`
`),jt=$(`\r
`),Ot=ot(jt,St,_t).desc("newline"),Ct=ot(Ot,pt);u.all=Ut,u.alt=ot,u.any=Qt,u.cr=_t,u.createLanguage=function(t){var n={};for(var r in t)({}).hasOwnProperty.call(t,r)&&function(e){n[e]=Bt(function(){return t[e](n)})}(r);return n},u.crlf=jt,u.custom=function(t){return u(t(h,b))},u.digit=Wt,u.digits=Tt,u.empty=lt,u.end=Ct,u.eof=pt,u.fail=it,u.formatError=bt,u.index=G,u.isParser=U,u.lazy=Bt,u.letter=$t,u.letters=Gt,u.lf=St,u.lookahead=at,u.makeFailure=b,u.makeSuccess=h,u.newline=Ot,u.noneOf=function(t){return ft(function(n){return t.indexOf(n)<0}).desc("none of '"+t+"'")},u.notFollowedBy=Et,u.of=P,u.oneOf=function(t){for(var n=t.split(""),r=0;r<n.length;r++)n[r]="'"+n[r]+"'";return ft(function(e){return t.indexOf(e)>=0}).desc(n)},u.optWhitespace=Jt,u.Parser=u,u.range=function(t,n){return ft(function(r){return t<=r&&r<=n}).desc(t+"-"+n)},u.regex=B,u.regexp=B,u.sepBy=wt,u.sepBy1=st,u.seq=ut,u.seqMap=k,u.seqObj=function(){for(var t,n={},r=0,e=(t=arguments,Array.prototype.slice.call(t)),o=e.length,i=0;i<o;i+=1){var a=e[i];if(!U(a)){if(q(a)&&a.length===2&&typeof a[0]=="string"&&U(a[1])){var s=a[0];if(Object.prototype.hasOwnProperty.call(n,s))throw new Error("seqObj: duplicate key "+s);n[s]=!0,r++;continue}throw new Error("seqObj arguments must be parsers or [string, parser] array pairs.")}}if(r===0)throw new Error("seqObj expects at least one named parser, found zero");return u(function(d,y){for(var v,_={},x=0;x<o;x+=1){var S,N;if(q(e[x])?(S=e[x][0],N=e[x][1]):(S=null,N=e[x]),!(v=w(N._(d,y),v)).status)return v;S&&(_[S]=v.value),y=v.index}return w(h(y,_),v)})},u.string=$,u.succeed=P,u.takeWhile=function(t){return L(t),u(function(n,r){for(var e=r;e<n.length&&t(nt(n,e));)e++;return h(e,n.slice(r,e))})},u.test=ft,u.whitespace=Zt,u["fantasy-land/empty"]=lt,u["fantasy-land/of"]=P,u.Binary={bitSeq:ht,bitSeqObj:function(t){C();var n={},r=0,e=j(function(i){if(q(i)){var a=i;if(a.length!==2)throw new Error("["+a.join(", ")+"] should be length 2, got length "+a.length);if(T(a[0]),F(a[1]),Object.prototype.hasOwnProperty.call(n,a[0]))throw new Error("duplicate key in bitSeqObj: "+a[0]);return n[a[0]]=!0,r++,a}return F(i),[null,i]},t);if(r<1)throw new Error("bitSeqObj expects at least one named pair, got ["+t.join(", ")+"]");var o=j(function(i){return i[0]},e);return ht(j(function(i){return i[1]},e)).map(function(i){return I(function(a,s){return s[0]!==null&&(a[s[0]]=s[1]),a},{},j(function(a,s){return[a,i[s]]},o))})},byte:function(t){if(C(),F(t),t>255)throw new Error("Value specified to byte constructor ("+t+"=0x"+t.toString(16)+") is larger in value than a single byte.");var n=(t>15?"0x":"0x0")+t.toString(16);return u(function(r,e){var o=nt(r,e);return o===t?h(e+1,o):b(e,n)})},buffer:function(t){return E("buffer",t).map(function(n){return Buffer.from(n)})},encodedString:function(t,n){return E("string",n).map(function(r){return r.toString(t)})},uintBE:V,uint8BE:V(1),uint16BE:V(2),uint32BE:V(4),uintLE:H,uint8LE:H(1),uint16LE:H(2),uint32LE:H(4),intBE:X,int8BE:X(1),int16BE:X(2),int32BE:X(4),intLE:Y,int8LE:Y(1),int16LE:Y(2),int32LE:Y(4),floatBE:E("floatBE",4).map(function(t){return t.readFloatBE(0)}),floatLE:E("floatLE",4).map(function(t){return t.readFloatLE(0)}),doubleBE:E("doubleBE",8).map(function(t){return t.readDoubleBE(0)}),doubleLE:E("doubleLE",8).map(function(t){return t.readDoubleLE(0)})},l.exports=u}])})});var g=en(Lt(),1),un=()=>g.default.createLanguage({entry:l=>g.default.alt(l.findReference,g.default.any).many().map(c=>c.flatMap(p=>p)).map(c=>c.filter(p=>typeof p=="object").flat().filter(p=>p!==null)),findReference:function(l){return g.default.seq(g.default.regex(/(import \* as m)|(import { m })/),l.findMessage.many())},dotNotation:()=>g.default.seqMap(g.default.string("."),g.default.index,g.default.regex(/\w+/),g.default.index,(l,c,p,u)=>({messageId:p,start:c,end:u})),doubleQuote:()=>g.default.seqMap(g.default.string('"'),g.default.index,g.default.regex(/[\w.]+/),g.default.string('"'),(l,c,p)=>({messageId:p,start:c})),singleQuote:()=>g.default.seqMap(g.default.string("'"),g.default.index,g.default.regex(/[\w.]+/),g.default.string("'"),(l,c,p)=>({messageId:p,start:c})),bracketNotation:l=>g.default.seqMap(g.default.string("["),g.default.alt(l.doubleQuote,l.singleQuote),g.default.string("]"),g.default.index,(c,p,u,f)=>({messageId:p.messageId,start:p.start,end:f})),findMessage:l=>g.default.seqMap(g.default.regex(/.*?(?<![a-zA-Z0-9/])m/s),g.default.alt(l.dotNotation,l.bracketNotation).or(g.default.succeed(null)),g.default.regex(/\((?:[^()]|\([^()]*\))*\)/).or(g.default.succeed("")),(c,p,u)=>p===null?null:{messageId:`${p.messageId}`,position:{start:{line:p.start.line,character:p.start.column},end:{line:p.end.line,character:p.end.column+u.length}}})});function kt(l){try{return un().entry.tryParse(l)}catch{return[]}}function ct(l){let c=l.trim().replace(/[^a-zA-Z0-9\s_.]/g,"").replace(/[\s.]+/g,"_");return/^[0-9]/.test(c)&&(c="_"+c),c}var Pt={messageReferenceMatchers:[async l=>kt(l.documentText)],extractMessageOptions:[{callback:l=>{let c=ct(l.bundleId);return{bundleId:c,messageReplacement:`{m.${c}()}`}}},{callback:l=>{let c=ct(l.bundleId);return{bundleId:c,messageReplacement:`m.${c}()`}}}],documentSelectors:[{language:"typescriptreact"},{language:"javascript"},{language:"typescript"},{language:"svelte"},{language:"astro"},{language:"vue"}]};var Mt="plugin.inlang.mFunctionMatcher",qt={id:Mt,displayName:"Inlang M Function Matcher",description:"A plugin for the inlang SDK that uses a JSON file per language tag to store translations.",key:Mt,meta:{"app.inlang.ideExtension":Pt}};var yn=qt;export{yn as default};
