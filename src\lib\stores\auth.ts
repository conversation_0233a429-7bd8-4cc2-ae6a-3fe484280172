// Authentication store

import { writable, derived } from 'svelte/store';
import type { TelegramUser } from '../types';
import { TelegramAuth } from '../telegram/auth';

interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: TelegramUser | null;
  sessionToken: string | null;
  error: string | null;
}

const initialState: AuthState = {
  isAuthenticated: false,
  isLoading: true,
  user: null,
  sessionToken: null,
  error: null
};

export const authStore = writable<AuthState>(initialState);

// Derived stores
export const isAuthenticated = derived(authStore, $auth => $auth.isAuthenticated);
export const currentUser = derived(authStore, $auth => $auth.user);
export const authLoading = derived(authStore, $auth => $auth.isLoading);
export const authError = derived(authStore, $auth => $auth.error);

// Actions
export const authActions = {
  
  /**
   * 初始化认证状态
   */
  async init() {
    authStore.update(state => ({ ...state, isLoading: true, error: null }));
    
    try {
      // 检查本地存储的会话
      const storedToken = TelegramAuth.getStoredSessionToken();
      const storedUser = TelegramAuth.getStoredUser();
      
      if (storedToken && storedUser) {
        // 验证存储的会话
        const validation = await TelegramAuth.validateSession(storedToken);
        
        if (validation.success && validation.user) {
          authStore.update(state => ({
            ...state,
            isAuthenticated: true,
            isLoading: false,
            user: validation.user,
            sessionToken: storedToken,
            error: null
          }));
          return;
        } else {
          // 清除无效的会话
          TelegramAuth.clearStoredAuth();
        }
      }
      
      authStore.update(state => ({
        ...state,
        isAuthenticated: false,
        isLoading: false,
        error: null
      }));
      
    } catch (error) {
      authStore.update(state => ({
        ...state,
        isAuthenticated: false,
        isLoading: false,
        error: error instanceof Error ? error.message : '初始化失败'
      }));
    }
  },

  /**
   * 使用 Telegram 数据登录
   */
  async loginWithTelegram(user: TelegramUser, referrerCode?: string) {
    authStore.update(state => ({ ...state, isLoading: true, error: null }));
    
    try {
      const session = await TelegramAuth.createSession(user, referrerCode);
      
      if (session.success && session.sessionToken) {
        // 存储会话信息
        TelegramAuth.storeSessionToken(session.sessionToken);
        TelegramAuth.storeUser(user);
        
        authStore.update(state => ({
          ...state,
          isAuthenticated: true,
          isLoading: false,
          user,
          sessionToken: session.sessionToken!,
          error: null
        }));
        
        return { success: true };
      } else {
        throw new Error(session.error || '登录失败');
      }
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '登录失败';
      
      authStore.update(state => ({
        ...state,
        isAuthenticated: false,
        isLoading: false,
        error: errorMessage
      }));
      
      return { success: false, error: errorMessage };
    }
  },

  /**
   * 登出
   */
  async logout() {
    authStore.update(state => ({ ...state, isLoading: true }));
    
    try {
      await TelegramAuth.logout();
      TelegramAuth.clearStoredAuth();
      
      authStore.set(initialState);
      
    } catch (error) {
      // 即使登出失败，也清除本地状态
      TelegramAuth.clearStoredAuth();
      authStore.set(initialState);
    }
  },

  /**
   * 更新用户信息
   */
  updateUser(user: Partial<TelegramUser>) {
    authStore.update(state => {
      if (state.user) {
        const updatedUser = { ...state.user, ...user };
        TelegramAuth.storeUser(updatedUser);
        return { ...state, user: updatedUser };
      }
      return state;
    });
  },

  /**
   * 清除错误
   */
  clearError() {
    authStore.update(state => ({ ...state, error: null }));
  }
};
