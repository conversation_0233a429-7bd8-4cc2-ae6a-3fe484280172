<script lang="ts">
	import { onMount } from 'svelte';
	import { isAuthenticated, userProfile, profileCompleteness } from '$lib/stores';
	import { formatPoints, formatVipLevel } from '$lib/utils/formatting';
	import { telegramWebApp } from '$lib/telegram';
	import Button from '$lib/components/ui/Button.svelte';
	import { 
		User, 
		Settings, 
		Share2, 
		Star, 
		Shield, 
		LogOut,
		Edit,
		Camera,
		MapPin,
		Calendar
	} from '@lucide/svelte';

	$: user = $userProfile;
	$: completeness = $profileCompleteness;

	function handleEditProfile() {
		// 跳转到编辑资料页面
		console.log('Edit profile');
	}

	function handleShareKinkMap() {
		if (user?.kinkMapCode) {
			telegramWebApp.shareKinkMap(user.kinkMapCode);
		}
	}

	function handleInviteFriend() {
		if (user?.kinkMapCode) {
			telegramWebApp.inviteFriend(user.kinkMapCode);
		}
	}

	function handleSettings() {
		// 跳转到设置页面
		console.log('Settings');
	}

	function handleLogout() {
		// 登出逻辑
		console.log('Logout');
	}
</script>

<svelte:head>
	<title>我的资料 - BlueX</title>
</svelte:head>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
	{#if !$isAuthenticated}
		<div class="flex items-center justify-center min-h-screen">
			<div class="text-center">
				<div class="text-4xl mb-4">🔐</div>
				<h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
					请先登录
				</h2>
				<p class="text-gray-600 dark:text-gray-300">
					需要登录后才能查看个人资料
				</p>
			</div>
		</div>
	{:else if !user}
		<div class="flex items-center justify-center min-h-screen">
			<div class="text-center">
				<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
				<p class="text-gray-600 dark:text-gray-300">加载中...</p>
			</div>
		</div>
	{:else}
		<!-- 页面头部 -->
		<header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
			<div class="px-4 py-4">
				<div class="flex items-center justify-between">
					<h1 class="text-xl font-bold text-gray-900 dark:text-white">
						我的资料
					</h1>
					<Button variant="ghost" size="icon" onclick={handleSettings}>
						<Settings class="w-5 h-5" />
					</Button>
				</div>
			</div>
		</header>

		<main class="p-4 space-y-6">
			<!-- 用户信息卡片 -->
			<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
				<!-- 头像和基本信息 -->
				<div class="p-6">
					<div class="flex items-center space-x-4">
						<div class="relative">
							{#if user.profileImageUrl}
								<img
									src={user.profileImageUrl}
									alt={user.nickname}
									class="w-20 h-20 rounded-full object-cover"
								/>
							{:else}
								<div class="w-20 h-20 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
									<User class="w-10 h-10 text-gray-600 dark:text-gray-300" />
								</div>
							{/if}
							<button class="absolute -bottom-1 -right-1 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center">
								<Camera class="w-3 h-3" />
							</button>
						</div>
						
						<div class="flex-1">
							<div class="flex items-center space-x-2">
								<h2 class="text-xl font-bold text-gray-900 dark:text-white">
									{user.nickname}
								</h2>
								{#if user.isVerified}
									<Shield class="w-5 h-5 text-blue-500" />
								{/if}
							</div>
							
							<div class="flex items-center space-x-4 mt-2 text-sm text-gray-600 dark:text-gray-300">
								{#if user.age}
									<div class="flex items-center space-x-1">
										<Calendar class="w-4 h-4" />
										<span>{user.age}岁</span>
									</div>
								{/if}
								{#if user.city || user.country}
									<div class="flex items-center space-x-1">
										<MapPin class="w-4 h-4" />
										<span>{user.city || user.country}</span>
									</div>
								{/if}
							</div>
							
							<div class="flex items-center space-x-2 mt-2">
								<span class="px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 text-xs rounded-full">
									{formatVipLevel(user.vipLevel)}
								</span>
								<span class="px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 text-xs rounded-full">
									信誉分 {user.trustScore}
								</span>
							</div>
						</div>
					</div>
					
					{#if user.bio}
						<p class="mt-4 text-gray-700 dark:text-gray-300">
							{user.bio}
						</p>
					{/if}
				</div>

				<!-- 资料完整度 -->
				<div class="px-6 py-4 bg-gray-50 dark:bg-gray-700/50 border-t border-gray-200 dark:border-gray-600">
					<div class="flex items-center justify-between mb-2">
						<span class="text-sm font-medium text-gray-700 dark:text-gray-300">
							资料完整度
						</span>
						<span class="text-sm font-bold text-blue-600 dark:text-blue-400">
							{completeness}%
						</span>
					</div>
					<div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
						<div 
							class="bg-blue-500 h-2 rounded-full transition-all duration-300"
							style="width: {completeness}%"
						></div>
					</div>
				</div>

				<!-- 操作按钮 -->
				<div class="p-4 border-t border-gray-200 dark:border-gray-600">
					<Button onclick={handleEditProfile} class="w-full mb-3">
						<Edit class="w-4 h-4 mr-2" />
						编辑资料
					</Button>
				</div>
			</div>

			<!-- 积分和等级 -->
			<div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
				<div class="flex items-center justify-between">
					<div>
						<h3 class="text-lg font-semibold mb-1">我的积分</h3>
						<p class="text-2xl font-bold">{formatPoints(user.pointBalance)}</p>
					</div>
					<Star class="w-8 h-8 text-yellow-300" />
				</div>
			</div>

			<!-- 功能菜单 -->
			<div class="space-y-3">
				<Button 
					variant="outline" 
					onclick={handleShareKinkMap}
					class="w-full justify-start"
				>
					<Share2 class="w-4 h-4 mr-3" />
					分享我的 Kink Map
				</Button>

				<Button 
					variant="outline" 
					onclick={handleInviteFriend}
					class="w-full justify-start"
				>
					<User class="w-4 h-4 mr-3" />
					邀请朋友
				</Button>

				<Button 
					variant="outline" 
					onclick={handleLogout}
					class="w-full justify-start text-red-600 border-red-200 hover:bg-red-50"
				>
					<LogOut class="w-4 h-4 mr-3" />
					退出登录
				</Button>
			</div>
		</main>
	{/if}
</div>
