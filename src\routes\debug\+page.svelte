<script lang="ts">
	import { onMount } from 'svelte';
	import { Button } from '$lib/components/ui/button';
	import { debugTelegramData, getTelegramDataFromAnySource, injectTelegramData } from '$lib/utils/telegram-data-parser';
	import { logTelegramDebugInfo, getTelegramDebugInfo } from '$lib/utils/debug';
	import { authActions, userProfile } from '$lib/stores';

	let debugInfo = $state<any>(null);
	let telegramData = $state<any>(null);
	let dbStatus = $state<any>(null);
	let authStatus = $state<any>(null);

	onMount(async () => {
		// 获取调试信息
		debugInfo = getTelegramDebugInfo();
		telegramData = getTelegramDataFromAnySource();
		
		// 检查数据库状态
		try {
			const response = await fetch('/api/debug/db-status');
			dbStatus = await response.json();
		} catch (error) {
			dbStatus = { error: 'Failed to check database status' };
		}

		// 检查认证状态
		authStatus = {
			isAuthenticated: !!$userProfile,
			user: $userProfile
		};
	});

	function runTelegramDebug() {
		debugTelegramData();
		logTelegramDebugInfo();
		// 刷新数据
		debugInfo = getTelegramDebugInfo();
		telegramData = getTelegramDataFromAnySource();
	}

	function injectSampleData() {
		const sampleData = 'user=%7B%22id%22%3A6662549305%2C%22first_name%22%3A%22Sher%22%2C%22last_name%22%3A%22Locked%22%2C%22language_code%22%3A%22zh-hans%22%2C%22allows_write_to_pm%22%3Atrue%2C%22photo_url%22%3A%22https%3A%5C%2F%5C%2Ft.me%5C%2Fi%5C%2Fuserpic%5C%2F320%5C%2FDdepy63fuyaEFYJihlJAK7hLpZg8faemv1v9KHbOpG0qfZbxe2eWH3aZRRmhCDFi.svg%22%7D&chat_instance=6891755669185758424&chat_type=private&auth_date=1750668363&signature=Rj5w3NzMUu93NJ6CCqZWoQ45WdNY9eqYcastZ_JEjKheFVDZpfimbaqnPVUONCP1oOxGyMKXBoG0HrsvsHphDA&hash=81ac3f3d0dc817c6b3a87b31505be0867a27b9f9f6e67b9bd64b461dbcfee434';
		
		if (injectTelegramData(sampleData)) {
			alert('✅ 示例数据已注入！请刷新页面。');
			window.location.reload();
		} else {
			alert('❌ 数据注入失败');
		}
	}

	async function testLogin() {
		if (telegramData?.user) {
			try {
				const result = await authActions.loginWithTelegram(telegramData.user);
				if (result.success) {
					alert('✅ 登录成功！');
					authStatus = {
						isAuthenticated: true,
						user: $userProfile
					};
				} else {
					alert('❌ 登录失败: ' + result.error);
				}
			} catch (error) {
				alert('❌ 登录错误: ' + error);
			}
		} else {
			alert('❌ 没有可用的Telegram用户数据');
		}
	}

	async function checkDatabase() {
		try {
			const response = await fetch('/api/debug/db-status');
			dbStatus = await response.json();
		} catch (error) {
			dbStatus = { error: 'Failed to check database status' };
		}
	}
</script>

<div class="container mx-auto p-4 max-w-4xl">
	<h1 class="text-2xl font-bold mb-6">🔍 TMA 调试工具</h1>

	<!-- 快速操作 -->
	<div class="mb-6 p-4 bg-blue-50 rounded-lg">
		<h2 class="text-lg font-semibold mb-3">🚀 快速操作</h2>
		<div class="flex flex-wrap gap-2">
			<Button onclick={runTelegramDebug}>运行Telegram调试</Button>
			<Button onclick={injectSampleData} variant="outline">注入示例数据</Button>
			<Button onclick={testLogin} variant="outline">测试登录</Button>
			<Button onclick={checkDatabase} variant="outline">检查数据库</Button>
		</div>
	</div>

	<!-- Telegram 环境信息 -->
	<div class="mb-6 p-4 border rounded-lg">
		<h2 class="text-lg font-semibold mb-3">📱 Telegram 环境</h2>
		{#if debugInfo}
			<div class="grid grid-cols-2 gap-4 text-sm">
				<div>
					<strong>环境:</strong> {debugInfo.hostname}
				</div>
				<div>
					<strong>User Agent:</strong> {debugInfo.userAgent.substring(0, 50)}...
				</div>
				<div>
					<strong>Telegram WebApp:</strong> 
					<span class={debugInfo.isTelegramWebApp ? 'text-green-600' : 'text-red-600'}>
						{debugInfo.isTelegramWebApp ? '✅ 是' : '❌ 否'}
					</span>
				</div>
				<div>
					<strong>WebApp 数据:</strong>
					<span class={debugInfo.hasWebAppData ? 'text-green-600' : 'text-red-600'}>
						{debugInfo.hasWebAppData ? '✅ 有' : '❌ 无'}
					</span>
				</div>
				<div>
					<strong>Session 数据:</strong>
					<span class={debugInfo.hasSessionData ? 'text-green-600' : 'text-red-600'}>
						{debugInfo.hasSessionData ? '✅ 有' : '❌ 无'}
					</span>
				</div>
				<div>
					<strong>URL 参数:</strong>
					<span class={debugInfo.hasUrlParams ? 'text-green-600' : 'text-red-600'}>
						{debugInfo.hasUrlParams ? '✅ 有' : '❌ 无'}
					</span>
				</div>
			</div>

			{#if debugInfo.errors.length > 0}
				<div class="mt-3 p-2 bg-red-50 rounded">
					<strong class="text-red-600">错误:</strong>
					<ul class="list-disc list-inside text-sm text-red-600">
						{#each debugInfo.errors as error}
							<li>{error}</li>
						{/each}
					</ul>
				</div>
			{/if}
		{/if}
	</div>

	<!-- Telegram 用户数据 -->
	<div class="mb-6 p-4 border rounded-lg">
		<h2 class="text-lg font-semibold mb-3">👤 Telegram 用户数据</h2>
		{#if telegramData}
			<div class="text-sm">
				<p><strong>数据源:</strong> {debugInfo?.telegramData?.source || 'Unknown'}</p>
				<p><strong>用户ID:</strong> {telegramData.user.id}</p>
				<p><strong>姓名:</strong> {telegramData.user.first_name} {telegramData.user.last_name || ''}</p>
				<p><strong>用户名:</strong> {telegramData.user.username || 'N/A'}</p>
				<p><strong>语言:</strong> {telegramData.user.language_code || 'N/A'}</p>
				<p><strong>头像:</strong> {telegramData.user.photo_url ? '✅ 有' : '❌ 无'}</p>
				{#if telegramData.auth_date}
					<p><strong>认证时间:</strong> {new Date(telegramData.auth_date * 1000).toLocaleString()}</p>
				{/if}
			</div>
		{:else}
			<p class="text-red-600">❌ 没有找到Telegram用户数据</p>
		{/if}
	</div>

	<!-- 数据库状态 -->
	<div class="mb-6 p-4 border rounded-lg">
		<h2 class="text-lg font-semibold mb-3">🗄️ 数据库状态</h2>
		{#if dbStatus}
			{#if dbStatus.connected}
				<div class="text-green-600">
					<p>✅ 数据库连接正常</p>
					<p class="text-sm">数据库: {dbStatus.database}</p>
					<p class="text-sm">用户数量: {dbStatus.userCount}</p>
					<p class="text-sm">检查时间: {new Date(dbStatus.timestamp).toLocaleString()}</p>
				</div>
			{:else}
				<div class="text-red-600">
					<p>❌ 数据库连接失败</p>
					<p class="text-sm">错误: {dbStatus.error}</p>
				</div>
			{/if}
		{:else}
			<p class="text-gray-600">⏳ 检查中...</p>
		{/if}
	</div>

	<!-- 认证状态 -->
	<div class="mb-6 p-4 border rounded-lg">
		<h2 class="text-lg font-semibold mb-3">🔐 认证状态</h2>
		{#if authStatus}
			{#if authStatus.isAuthenticated}
				<div class="text-green-600">
					<p>✅ 已认证</p>
					{#if authStatus.user}
						<p class="text-sm">用户: {authStatus.user.nickname} (ID: {authStatus.user.id})</p>
						<p class="text-sm">积分: {authStatus.user.pointBalance}</p>
						<p class="text-sm">完整度: {authStatus.user.profileCompletenessScore}%</p>
					{/if}
				</div>
			{:else}
				<p class="text-red-600">❌ 未认证</p>
			{/if}
		{:else}
			<p class="text-gray-600">⏳ 检查中...</p>
		{/if}
	</div>

	<!-- 调试说明 -->
	<div class="p-4 bg-gray-50 rounded-lg">
		<h2 class="text-lg font-semibold mb-3">📖 调试说明</h2>
		<div class="text-sm space-y-2">
			<p><strong>1. 在Telegram中测试:</strong> 通过BotFather设置的Web App链接打开</p>
			<p><strong>2. 本地开发测试:</strong> 点击"注入示例数据"按钮，然后刷新页面</p>
			<p><strong>3. 数据库问题:</strong> 确保PostgreSQL运行并且.env文件配置正确</p>
			<p><strong>4. 认证问题:</strong> 检查Telegram数据是否正确获取，然后点击"测试登录"</p>
			<p><strong>5. 查看控制台:</strong> 打开浏览器开发者工具查看详细日志</p>
		</div>
	</div>
</div>
