// UI-related type definitions

export type TabId = 'discover' | 'interactions' | 'growth' | 'profile';

export interface TabItem {
  id: TabId;
  label: string;
  icon: string;
  path: string;
  requiresAuth?: boolean;
  requiresBasicProfile?: boolean;
  requiresAdvancedProfile?: boolean;
}

export type ModalType = 
  | 'onboarding'
  | 'basic_profile_setup'
  | 'advanced_profile_setup'
  | 'kink_preferences'
  | 'privacy_settings'
  | 'search_filters'
  | 'user_profile'
  | 'match_confirmation';

export interface ModalState {
  isOpen: boolean;
  type?: ModalType;
  data?: any;
  onClose?: () => void;
  onConfirm?: (data?: any) => void;
}

export interface ToastMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  duration?: number;
  action?: {
    label: string;
    handler: () => void;
  };
}

export interface LoadingState {
  isLoading: boolean;
  message?: string;
  progress?: number;
}

export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'number' | 'select' | 'multiselect' | 'range' | 'checkbox' | 'radio';
  required?: boolean;
  placeholder?: string;
  options?: Array<{ value: string | number; label: string }>;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    custom?: (value: any) => string | null;
  };
}

export interface StepperStep {
  id: string;
  title: string;
  description?: string;
  fields: FormField[];
  isCompleted?: boolean;
  isOptional?: boolean;
}
