// Telegram WebApp utilities

import { browser } from '$app/environment';
import type { TelegramInitData, TelegramWebApp } from '../types';
import { getTelegramDataFromAnySource, validateTelegramData } from '../utils/telegram-data-parser';

export class TelegramWebAppManager {
	private webApp: TelegramWebApp | null = null;
	private isInitialized = false;

	constructor() {
		if (browser) {
			this.init();
		}
	}

	private init() {
		try {
			if (typeof window !== 'undefined' && window.Telegram?.WebApp) {
				this.webApp = window.Telegram.WebApp;
				this.webApp.ready();
				this.isInitialized = true;

				// 设置主题
				this.setupTheme();

				// 展开应用
				this.webApp.expand();

				// 启用关闭确认
				this.webApp.isClosingConfirmationEnabled = true;
			}
		} catch (error) {
			console.error('Failed to initialize Telegram WebApp:', error);
		}
	}

	private setupTheme() {
		if (!this.webApp) return;

		try {
			// 设置头部颜色
			if (this.webApp.colorScheme === 'dark') {
				this.webApp.headerColor = '#1a1a1a';
				this.webApp.backgroundColor = '#000000';
			} else {
				this.webApp.headerColor = '#ffffff';
				this.webApp.backgroundColor = '#ffffff';
			}
		} catch (error) {
			console.warn('Failed to setup theme:', error);
		}
	}

	public getInitData(): TelegramInitData | null {
		// 使用新的数据解析器获取Telegram数据
		const telegramData = getTelegramDataFromAnySource();

		if (telegramData && validateTelegramData(telegramData)) {
			return {
				user: telegramData.user,
				start_param: telegramData.start_param,
				referrer_code: this.extractReferrerCode(telegramData.start_param),
				is_new_user: false
			};
		}

		return null;
	}

	private extractReferrerCode(startParam?: string): string | undefined {
		if (!startParam) return undefined;

		// 解析 start_param 中的邀请码
		// 格式可能是 "ref_ABC123" 或 "invite_XYZ789"
		const match = startParam.match(/^(ref|invite)_([A-Z0-9]+)$/);
		return match ? match[2] : undefined;
	}

	public isReady(): boolean {
		// 检查是否在 Telegram 环境中
		if (this.isInitialized && this.webApp !== null) {
			return true;
		}

		// 使用新的数据解析器检查是否有可用的Telegram数据
		const telegramData = getTelegramDataFromAnySource();
		if (telegramData && validateTelegramData(telegramData)) {
			return true;
		}

		// 开发环境下的特殊处理
		if (
			typeof window !== 'undefined' &&
			(window.location.hostname === 'localhost' ||
				window.location.hostname === '127.0.0.1' ||
				window.location.hostname === '*************')
		) {
			console.warn(
				'Development mode: Telegram WebApp not detected, but allowing access for debugging'
			);
			return true;
		}

		return false;
	}

	public showAlert(message: string): void {
		if (this.webApp) {
			this.webApp.showAlert(message);
		} else {
			alert(message);
		}
	}

	public showConfirm(message: string): Promise<boolean> {
		return new Promise((resolve) => {
			if (this.webApp) {
				this.webApp.showConfirm(message);
				// 注意：实际的确认结果需要通过事件监听获取
				// 这里简化处理
				resolve(true);
			} else {
				resolve(confirm(message));
			}
		});
	}

	public showPopup(params: {
		title?: string;
		message: string;
		buttons?: Array<{
			id?: string;
			type?: 'default' | 'ok' | 'close' | 'cancel' | 'destructive';
			text: string;
		}>;
	}): void {
		if (this.webApp) {
			this.webApp.showPopup(params);
		} else {
			this.showAlert(params.message);
		}
	}

	public close(): void {
		if (this.webApp) {
			this.webApp.close();
		}
	}

	public sendData(data: any): void {
		if (this.webApp) {
			this.webApp.sendData(JSON.stringify(data));
		}
	}

	public openLink(url: string): void {
		if (this.webApp) {
			this.webApp.openLink(url);
		} else {
			window.open(url, '_blank');
		}
	}

	public openTelegramLink(url: string): void {
		if (this.webApp) {
			this.webApp.openTelegramLink(url);
		} else {
			this.openLink(url);
		}
	}

	public getTheme() {
		if (!this.webApp) return { colorScheme: 'light', themeParams: {} };

		return {
			colorScheme: this.webApp.colorScheme,
			themeParams: this.webApp.themeParams
		};
	}

	public getViewportHeight(): number {
		return this.webApp?.viewportHeight || window.innerHeight;
	}

	public getViewportStableHeight(): number {
		return this.webApp?.viewportStableHeight || window.innerHeight;
	}

	public onEvent(eventType: string, handler: Function): void {
		if (this.webApp) {
			this.webApp.onEvent(eventType, handler);
		}
	}

	public offEvent(eventType: string, handler: Function): void {
		if (this.webApp) {
			this.webApp.offEvent(eventType, handler);
		}
	}

	// 分享 Kink Map
	public shareKinkMap(kinkMapCode: string): void {
		const shareUrl = `https://t.me/${this.getShareBotUsername()}?start=kink_${kinkMapCode}`;
		const shareText = `查看我的 Kink Map！使用 BlueX 发现更多有趣的人 🔥`;

		this.sendData({
			action: 'share',
			type: 'kink_map',
			url: shareUrl,
			text: shareText
		});
	}

	private getShareBotUsername(): string {
		// 这里应该返回你的 bot username
		return 'sveltekit_bot'; // 替换为实际的 bot username
	}

	// 邀请朋友
	public inviteFriend(referrerCode: string): void {
		const inviteUrl = `https://t.me/${this.getShareBotUsername()}?start=ref_${referrerCode}`;
		const inviteText = `加入 BlueX，发现志同道合的朋友！🌟`;

		this.sendData({
			action: 'share',
			type: 'invite',
			url: inviteUrl,
			text: inviteText
		});
	}
}

// 单例实例
export const telegramWebApp = new TelegramWebAppManager();
