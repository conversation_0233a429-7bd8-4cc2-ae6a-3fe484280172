<script lang="ts">
	import '../app.css';
	import AuthGuard from '$lib/components/AuthGuard.svelte';
	import TabNavigation from '$lib/components/TabNavigation.svelte';
	import Modal from '$lib/components/Modal.svelte';
	import Toast from '$lib/components/Toast.svelte';
	import LoadingOverlay from '$lib/components/LoadingOverlay.svelte';

	let { children } = $props();
</script>

<div class="app-container min-h-screen bg-gray-50 dark:bg-gray-900">
	<AuthGuard>
		<main class="pb-16">
			{@render children()}
		</main>

		<TabNavigation />
	</AuthGuard>

	<!-- Global UI Components -->
	<Modal />
	<Toast />
	<LoadingOverlay />
</div>
