<script lang="ts">
	import '../app.css';
	import { onMount } from 'svelte';
	import { authActions, userActions } from '$lib/stores';
	import { telegramWebApp } from '$lib/telegram';
	import TelegramInit from '$lib/components/TelegramInit.svelte';
	import TabNavigation from '$lib/components/TabNavigation.svelte';
	import Modal from '$lib/components/Modal.svelte';
	import Toast from '$lib/components/Toast.svelte';
	import LoadingOverlay from '$lib/components/LoadingOverlay.svelte';

	let { children } = $props();
	let isInitialized = $state(false);

	onMount(async () => {
		// 初始化认证状态
		await authActions.init();
		isInitialized = true;
	});
</script>

<div class="app-container min-h-screen bg-gray-50 dark:bg-gray-900">
	{#if !isInitialized}
		<TelegramInit />
	{:else}
		<main class="pb-16">
			{@render children()}
		</main>

		<TabNavigation />
	{/if}

	<!-- Global UI Components -->
	<Modal />
	<Toast />
	<LoadingOverlay />
</div>
