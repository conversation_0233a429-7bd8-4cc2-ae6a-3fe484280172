// Search store

import { writable, derived } from 'svelte/store';
import type { SearchOptions, SearchResult, SearchState, SearchMode } from '../types';

const initialState: SearchState = {
  isSearching: false,
  results: [],
  hasMore: false,
  error: undefined,
  lastSearchTime: undefined
};

export const searchStore = writable<SearchState>(initialState);

// Current search options
export const searchOptions = writable<SearchOptions>({
  mode: 'basic',
  filters: {},
  sortBy: 'relevance',
  limit: 20,
  offset: 0
});

// Derived stores
export const searchResults = derived(searchStore, $search => $search.results);
export const isSearching = derived(searchStore, $search => $search.isSearching);
export const searchError = derived(searchStore, $search => $search.error);
export const hasMoreResults = derived(searchStore, $search => $search.hasMore);

// Actions
export const searchActions = {
  
  /**
   * 执行搜索
   */
  async search(options: SearchOptions) {
    searchStore.update(state => ({ 
      ...state, 
      isSearching: true, 
      error: undefined 
    }));
    
    searchOptions.set(options);
    
    try {
      const response = await fetch('/api/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(options)
      });
      
      if (!response.ok) {
        throw new Error('搜索失败');
      }
      
      const data = await response.json();
      
      searchStore.update(state => ({
        ...state,
        isSearching: false,
        results: options.offset === 0 ? data.results : [...state.results, ...data.results],
        hasMore: data.hasMore,
        lastSearchTime: new Date(),
        error: undefined
      }));
      
      return { success: true, results: data.results };
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '搜索失败';
      
      searchStore.update(state => ({
        ...state,
        isSearching: false,
        error: errorMessage
      }));
      
      return { success: false, error: errorMessage };
    }
  },

  /**
   * 加载更多结果
   */
  async loadMore() {
    const currentOptions = await new Promise<SearchOptions>(resolve => {
      const unsubscribe = searchOptions.subscribe(options => {
        unsubscribe();
        resolve(options);
      });
    });
    
    const currentState = await new Promise<SearchState>(resolve => {
      const unsubscribe = searchStore.subscribe(state => {
        unsubscribe();
        resolve(state);
      });
    });
    
    if (!currentState.hasMore || currentState.isSearching) {
      return;
    }
    
    const newOptions = {
      ...currentOptions,
      offset: currentState.results.length
    };
    
    return this.search(newOptions);
  },

  /**
   * 切换搜索模式
   */
  setSearchMode(mode: SearchMode) {
    searchOptions.update(options => ({
      ...options,
      mode,
      offset: 0 // 重置偏移量
    }));
  },

  /**
   * 更新搜索过滤器
   */
  updateFilters(filters: any) {
    searchOptions.update(options => ({
      ...options,
      filters: { ...options.filters, ...filters },
      offset: 0 // 重置偏移量
    }));
  },

  /**
   * 更新排序方式
   */
  setSortBy(sortBy: 'relevance' | 'distance' | 'lastActive' | 'trustScore') {
    searchOptions.update(options => ({
      ...options,
      sortBy,
      offset: 0 // 重置偏移量
    }));
  },

  /**
   * 清除搜索结果
   */
  clearResults() {
    searchStore.update(state => ({
      ...state,
      results: [],
      hasMore: false,
      error: undefined
    }));
  },

  /**
   * 重置搜索状态
   */
  reset() {
    searchStore.set(initialState);
    searchOptions.update(options => ({
      ...options,
      offset: 0
    }));
  },

  /**
   * 清除错误
   */
  clearError() {
    searchStore.update(state => ({ ...state, error: undefined }));
  }
};
