// Search-related type definitions

import type { UserProfile } from './user';

export type SearchMode = 'basic' | 'advanced' | 'super';

export interface BasicSearchFilters {
	ageRange?: [number, number];
	location?: {
		country?: string;
		city?: string;
		radius?: number; // km
	};
	orientation?: string[];
	bodyType?: string[];
	presentationStyle?: string[];
}

export interface AdvancedSearchFilters extends BasicSearchFilters {
	kinkCategories?: number[]; // bitmask values
	kinkRatings?: {
		[kinkCode: string]: {
			min?: number; // -1 to 5
			max?: number;
		};
	};
	relationshipStatus?: string[];
	profileCompleteness?: {
		minScore?: number;
		hasAvatar?: boolean;
		isVerified?: boolean;
	};
	trustScore?: {
		min?: number;
		max?: number;
	};
}

export interface SuperSearchFilters extends AdvancedSearchFilters {
	vipLevel?: number[];
	lastActiveWithin?: number; // hours
	excludeAlreadyInteracted?: boolean;
}

export interface SearchResult {
	user: UserProfile;
	matchScore?: number;
	distance?: number;
	commonKinks?: string[];
	isOnline?: boolean;
}

export interface SearchOptions {
	mode: SearchMode;
	filters: BasicSearchFilters | AdvancedSearchFilters | SuperSearchFilters;
	sortBy?: 'relevance' | 'distance' | 'lastActive' | 'trustScore';
	limit?: number;
	offset?: number;
}

export interface SearchState {
	isSearching: boolean;
	results: SearchResult[];
	hasMore: boolean;
	error?: string;
	lastSearchTime?: Date;
}
