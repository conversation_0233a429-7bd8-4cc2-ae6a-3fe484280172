import { fail } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { kinkPreferencesSchema, privacySettingsSchema } from '$lib/schemas/user';
import { z } from 'zod';
import type { Actions, PageServerLoad } from './$types';

// 合并 schema
const advancedSchema = kinkPreferencesSchema.merge(privacySettingsSchema);

export const load: PageServerLoad = async () => {
	return {
		form: await superValidate(zod(advancedSchema))
	};
};

export const actions: Actions = {
	default: async (event) => {
		const form = await superValidate(event, zod(advancedSchema));

		if (!form.valid) {
			return fail(400, {
				form
			});
		}

		try {
			// 这里处理保存用户高级偏好的逻辑
			// const userId = event.locals.user?.id;
			// await updateUserAdvancedPreferences(userId, form.data);

			console.log('Advanced preferences data:', form.data);

			// 重定向到发现页面
			// throw redirect(302, '/discover');

			return {
				form
			};
		} catch (error) {
			console.error('Failed to save advanced preferences:', error);
			return fail(500, {
				form,
				message: '保存失败，请重试'
			});
		}
	}
};
