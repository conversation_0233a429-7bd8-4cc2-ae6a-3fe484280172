<script lang="ts">
	import { onMount } from 'svelte';
	import { authActions } from '$lib/stores';
	import { telegramWebApp } from '$lib/telegram';
	import { uiActions } from '$lib/stores';
	import { logTelegramDebugInfo, getTelegramDebugInfo } from '$lib/utils/debug';

	let isInitializing = $state(true);
	let error = $state<string | null>(null);
	let debugInfo = $state<any>(null);

	onMount(async () => {
		try {
			// 开发环境下显示调试信息
			if (
				typeof window !== 'undefined' &&
				(window.location.hostname === 'localhost' ||
					window.location.hostname === '127.0.0.1' ||
					window.location.hostname === '*************')
			) {
				logTelegramDebugInfo();
				debugInfo = getTelegramDebugInfo();
			}

			// 等待 Telegram WebApp 准备就绪
			if (!telegramWebApp.isReady()) {
				// 如果不在 Telegram 环境中，显示提示
				error = '请在 Telegram 中打开此应用';
				isInitializing = false;
				return;
			}

			// 获取 Telegram 初始化数据
			const initData = telegramWebApp.getInitData();

			if (!initData || !initData.user) {
				error = '无法获取用户信息';
				isInitializing = false;
				return;
			}

			$inspect('🚀 Telegram 用户数据:', initData.user);

			// 尝试登录
			const result = await authActions.loginWithTelegram(initData.user, initData.referrer_code);

			if (!result.success) {
				error = result.error || '登录失败';
			} else {
				console.log('✅ 登录成功');
			}
		} catch (err) {
			console.error('❌ 初始化失败:', err);
			error = err instanceof Error ? err.message : '初始化失败';
		} finally {
			isInitializing = false;
		}
	});

	function retry() {
		error = null;
		isInitializing = true;
		// 重新执行初始化逻辑
		setTimeout(() => {
			onMount(async () => {});
		}, 100);
	}
</script>

<div
	class="flex min-h-screen items-center justify-center bg-gradient-to-br from-blue-500 to-purple-600"
>
	<div class="mx-4 w-full max-w-md rounded-lg bg-white p-8 shadow-xl">
		<div class="text-center">
			<div class="mb-6">
				<div class="mb-2 text-4xl">🔥</div>
				<h1 class="text-2xl font-bold text-gray-900">BlueX</h1>
				<p class="mt-2 text-gray-600">发现志同道合的朋友</p>
			</div>

			{#if isInitializing}
				<div class="space-y-4">
					<div class="mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-blue-500"></div>
					<p class="text-gray-600">正在初始化...</p>
				</div>
			{:else if error}
				<div class="space-y-4">
					<div class="rounded-lg bg-red-50 p-3 text-sm text-red-500">
						{error}
					</div>

					<!-- 开发环境下显示调试信息 -->
					{#if debugInfo && (debugInfo.hostname === 'localhost' || debugInfo.hostname === '127.0.0.1')}
						<details class="text-left">
							<summary class="cursor-pointer text-sm text-gray-600">🔍 调试信息</summary>
							<div class="mt-2 rounded bg-gray-100 p-2 text-xs">
								<p><strong>环境:</strong> {debugInfo.hostname}</p>
								<p><strong>Telegram WebApp:</strong> {debugInfo.isTelegramWebApp ? '✅' : '❌'}</p>
								<p><strong>WebApp 数据:</strong> {debugInfo.hasWebAppData ? '✅' : '❌'}</p>
								<p><strong>Session 数据:</strong> {debugInfo.hasSessionData ? '✅' : '❌'}</p>
								<p><strong>URL 参数:</strong> {debugInfo.hasUrlParams ? '✅' : '❌'}</p>
								{#if debugInfo.telegramData}
									<p><strong>数据源:</strong> {debugInfo.telegramData.source}</p>
									<p>
										<strong>用户:</strong>
										{debugInfo.telegramData.user?.first_name} ({debugInfo.telegramData.user?.id})
									</p>
								{/if}
								{#if debugInfo.errors.length > 0}
									<p><strong>错误:</strong></p>
									<ul class="list-inside list-disc">
										{#each debugInfo.errors as err}
											<li class="text-red-600">{err}</li>
										{/each}
									</ul>
								{/if}
							</div>
						</details>
					{/if}

					<button
						onclick={retry}
						class="w-full rounded-lg bg-blue-500 px-4 py-2 text-white transition-colors hover:bg-blue-600"
					>
						重试
					</button>
				</div>
			{:else}
				<div class="space-y-4">
					<div class="text-green-500">
						<svg class="mx-auto h-8 w-8" fill="currentColor" viewBox="0 0 20 20">
							<path
								fill-rule="evenodd"
								d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
								clip-rule="evenodd"
							></path>
						</svg>
					</div>
					<p class="text-gray-600">初始化完成</p>
				</div>
			{/if}
		</div>
	</div>
</div>
