<script lang="ts">
	import { onMount } from 'svelte';
	import { authActions } from '$lib/stores';
	import { telegramWebApp } from '$lib/telegram';
	import { uiActions } from '$lib/stores';

	let isInitializing = $state(true);
	let error = $state<string | null>(null);

	onMount(async () => {
		try {
			// 等待 Telegram WebApp 准备就绪
			if (!telegramWebApp.isReady()) {
				// 如果不在 Telegram 环境中，显示提示
				error = '请在 Telegram 中打开此应用';
				isInitializing = false;
				return;
			}

			// 获取 Telegram 初始化数据
			const initData = telegramWebApp.getInitData();
			
			if (!initData || !initData.user) {
				error = '无法获取用户信息';
				isInitializing = false;
				return;
			}

			// 尝试登录
			const result = await authActions.loginWithTelegram(
				initData.user, 
				initData.referrer_code
			);

			if (!result.success) {
				error = result.error || '登录失败';
			}

		} catch (err) {
			error = err instanceof Error ? err.message : '初始化失败';
		} finally {
			isInitializing = false;
		}
	});

	function retry() {
		error = null;
		isInitializing = true;
		// 重新执行初始化逻辑
		onMount(() => {});
	}
</script>

<div class="flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-500 to-purple-600">
	<div class="bg-white rounded-lg shadow-xl p-8 max-w-md w-full mx-4">
		<div class="text-center">
			<div class="mb-6">
				<div class="text-4xl mb-2">🔥</div>
				<h1 class="text-2xl font-bold text-gray-900">BlueX</h1>
				<p class="text-gray-600 mt-2">发现志同道合的朋友</p>
			</div>

			{#if isInitializing}
				<div class="space-y-4">
					<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
					<p class="text-gray-600">正在初始化...</p>
				</div>
			{:else if error}
				<div class="space-y-4">
					<div class="text-red-500 text-sm bg-red-50 p-3 rounded-lg">
						{error}
					</div>
					<button 
						onclick={retry}
						class="w-full bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors"
					>
						重试
					</button>
				</div>
			{:else}
				<div class="space-y-4">
					<div class="text-green-500">
						<svg class="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
						</svg>
					</div>
					<p class="text-gray-600">初始化完成</p>
				</div>
			{/if}
		</div>
	</div>
</div>
