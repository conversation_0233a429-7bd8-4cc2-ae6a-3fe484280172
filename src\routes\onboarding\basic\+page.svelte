<script lang="ts">
	import { goto } from '$app/navigation';
	import BasicProfileForm from '$lib/components/forms/BasicProfileForm.svelte';
	import { uiActions } from '$lib/stores';
	import type { PageData } from './$types';
	import type { BasicProfile } from '$lib/schemas/user';

	let { data }: { data: PageData } = $props();

	function handleSubmit(profileData: BasicProfile) {
		// 显示成功消息
		uiActions.toast.success('基础资料设置完成！');
		
		// 跳转到发现页面
		goto('/discover');
	}

	function handleCancel() {
		// 跳转到发现页面，但提醒功能受限
		uiActions.toast.warning('跳过设置后，搜索功能将受到限制');
		goto('/discover');
	}
</script>

<svelte:head>
	<title>完善基础资料 - BlueX</title>
	<meta name="description" content="完善您的基础资料以获得更好的匹配体验" />
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
	<BasicProfileForm 
		data={data.form} 
		onSubmit={handleSubmit}
		onCancel={handleCancel}
	/>
</div>
