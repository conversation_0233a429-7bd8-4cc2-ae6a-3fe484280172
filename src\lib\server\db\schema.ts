import {
	pgTable,
	uuid,
	bigint,
	text,
	integer,
	boolean,
	timestamp,
	jsonb,
	primaryKey,
	index,
	pgEnum
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
// import type { AdapterAccount } from '@auth/core/adapters'; // 如果你使用 Auth.js/Drizzle adapter, 可能会用到这个

// =================================================================
// SECTION 1: ENUM DEFINITIONS
// (使用 pgEnum 将 PostgreSQL 的 ENUM 类型映射到 TypeScript)
// =================================================================

export const orientationEnum = pgEnum('orientation_enum', [
	'straight',
	'gay',
	'lesbian',
	'bisexual',
	'asexual',
	'demisexual',
	'pansexual',
	'queer',
	'fluid',
	'other_orientation',
	'prefer_not_to_say_orientation'
]);
export const bodyTypeEnum = pgEnum('body_type_enum', [
	'male_body',
	'female_body',
	'other_body_type'
]);
export const presentationStyleEnum = pgEnum('presentation_style_enum', [
	'conventional_masculine',
	'rugged_masculine',
	'feminine',
	'androgynous_neutral',
	'other_presentation_style'
]);
export const relationshipStatusEnum = pgEnum('relationship_status_enum', [
	'single',
	'in_a_relationship',
	'complicated',
	'open_relationship',
	'married',
	'polyamorous',
	'other_relationship_status',
	'prefer_not_to_say_relationship_status'
]);
export const completenessFilterEnum = pgEnum('completeness_filter_enum', [
	'has_avatar',
	'no_avatar',
	'is_profile_complete',
	'is_profile_incomplete',
	'is_verified',
	'is_unverified'
]);

export const matchStatusEnum = pgEnum('match_status_enum', [
	'liked', // 单向喜欢
	'matched', // 双向匹配
	'blocked' // 硬屏蔽
]);

export const pointTransactionTypeEnum = pgEnum('point_transaction_type_enum', [
	'registration_bonus',
	'daily_check_in',
	'profile_completion_bonus',
	'search_cost',
	'super_search_cost',
	'super_like_cost',
	'invite_bonus',
	'top_up',
	'system_adjustment'
]);

// =================================================================
// SECTION 2: TABLE DEFINITIONS
// =================================================================

/**
 * 核心用户表 (users)
 * 存储所有用户的核心画像、状态和隐私设置。
 */
export const users = pgTable(
	'users',
	{
		// --- 核心标识符 ---
		id: uuid('id').primaryKey().defaultRandom(),
		telegramUserId: bigint('telegram_user_id', { mode: 'number' }).notNull().unique(),
		telegramUsername: text('telegram_username'),
		kinkMapCode: text('kink_map_code').notNull().unique(),

		// --- 核心用户画像 ---
		nickname: text('nickname').notNull(),
		age: integer('age'),
		orientation: orientationEnum('orientation'),
		bodyType: bodyTypeEnum('body_type'),
		presentationStyle: presentationStyleEnum('presentation_style'),

		// --- 补充用户画像 ---
		bio: text('bio'),
		heightCm: integer('height_cm'),
		weightKg: integer('weight_kg'),
		relationshipStatus: relationshipStatusEnum('relationship_status'),
		country: text('country'),
		city: text('city'),
		profileImageUrl: text('profile_image_url'),

		// --- 系统计算与状态 ---
		hasAvatar: boolean('has_avatar').notNull().default(false),
		profileCompletenessScore: integer('profile_completeness_score').notNull().default(0),
		isVerified: boolean('is_verified').notNull().default(false),
		trustScore: integer('trust_score').notNull().default(100),

		// --- 商业化与积分 ---
		vipLevel: integer('vip_level').notNull().default(0),
		pointBalance: integer('point_balance').notNull().default(0),

		// --- Kink 偏好 ---
		kinkCategoryBitmask: bigint('kink_category_bitmask', { mode: 'number' }).notNull().default(0),
		kinkRatings: jsonb('kink_ratings').notNull().default({}),

		// --- 隐私设置/软屏蔽 (数组类型) ---
		blockVisibilityFromBodyTypes: bodyTypeEnum('block_visibility_from_body_types').array(),
		blockVisibilityFromPresentationStyles: presentationStyleEnum(
			'block_visibility_from_presentation_styles'
		).array(),
		blockVisibilityFromOrientations: orientationEnum('block_visibility_from_orientations').array(),

		// --- 系统管理与时间戳 ---
		createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
		updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
		lastActiveAt: timestamp('last_active_at', { withTimezone: true }).notNull().defaultNow(),
		isActive: boolean('is_active').notNull().default(true),
		isBanned: boolean('is_banned').notNull().default(false)
	},
	(table) => {
		// --- 索引定义 ---
		return [
			index('idx_users_search_sorting').on(table.trustScore, table.lastActiveAt),
			index('idx_users_common_filters').on(
				table.age,
				table.country,
				table.city,
				table.presentationStyle
			),
			index('idx_users_kink_ratings_gin').on(table.kinkRatings),
			index('idx_users_block_body_types_gin').on(table.blockVisibilityFromBodyTypes),
			index('idx_users_block_presentation_styles_gin').on(
				table.blockVisibilityFromPresentationStyles
			),
			index('idx_users_block_orientations_gin').on(table.blockVisibilityFromOrientations)
		];
	}
);

/**
 * 用户交互表 (matches)
 * 记录用户之间的 "like", "match", 和 "block" 关系。
 */
export const matches = pgTable(
	'matches',
	{
		actorUserId: uuid('actor_user_id')
			.notNull()
			.references(() => users.id, { onDelete: 'cascade' }),
		targetUserId: uuid('target_user_id')
			.notNull()
			.references(() => users.id, { onDelete: 'cascade' }),
		status: matchStatusEnum('status').notNull(),
		createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
		updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow()
	},
	(table) => {
		// --- 复合主键和索引 ---
		return [
			primaryKey({ columns: [table.actorUserId, table.targetUserId] }),
			index('idx_matches_incoming_interactions').on(table.targetUserId, table.status)
		];
	}
);

/**
 * 积分流水表 (point_transactions)
 * 像银行账本一样记录每一次积分的变动，确保可追溯。
 */
export const pointTransactions = pgTable(
	'point_transactions',
	{
		id: uuid('id').primaryKey().defaultRandom(),
		userId: uuid('user_id')
			.notNull()
			.references(() => users.id, { onDelete: 'cascade' }),
		amount: integer('amount').notNull(),
		type: pointTransactionTypeEnum('type').notNull(),
		description: text('description'),
		referenceId: text('reference_id'),
		createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow()
	},
	(table) => {
		// --- 索引 ---
		return [
			index('idx_point_transactions_user_id').on(table.userId),
			index('idx_point_transactions_type').on(table.type)
		];
	}
);

// =================================================================
// SECTION 3: RELATIONS DEFINITIONS
// (定义表之间的关系，方便使用 Drizzle ORM 进行 JOIN 查询)
// =================================================================

export const usersRelations = relations(users, ({ many }) => ({
	/** 该用户发出的所有交互 (likes, blocks) */
	interactionsInitiated: many(matches, { relationName: 'initiated_matches' }),
	/** 该用户收到的所有交互 */
	interactionsReceived: many(matches, { relationName: 'received_matches' }),
	/** 该用户的所有积分流水记录 */
	pointTransactions: many(pointTransactions)
}));

export const matchesRelations = relations(matches, ({ one }) => ({
	/** 发起该次交互的用户信息 */
	actor: one(users, {
		fields: [matches.actorUserId],
		references: [users.id],
		relationName: 'initiated_matches'
	}),
	/** 接收该次交互的用户信息 */
	target: one(users, {
		fields: [matches.targetUserId],
		references: [users.id],
		relationName: 'received_matches'
	})
}));

export const pointTransactionsRelations = relations(pointTransactions, ({ one }) => ({
	/** 该条流水所属的用户 */
	user: one(users, {
		fields: [pointTransactions.userId],
		references: [users.id]
	})
}));
