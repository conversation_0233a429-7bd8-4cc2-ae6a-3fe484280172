import {
	pgTable,
	uuid,
	bigint,
	text,
	integer,
	boolean,
	timestamp,
	jsonb,
	primaryKey,
	index,
	pgEnum
} from 'drizzle-orm/pg-core';

import { relations } from 'drizzle-orm';

export const orientationEnum = pgEnum('orientation_enum', [
	'straight',
	'gay',
	'lesbian',
	'bisexual',
	'asexual',
	'demisexual',
	'pansexual',
	'queer',
	'fluid',
	'other_orientation',
	'prefer_not_to_say_orientation'
]);

export const bodyTypeEnum = pgEnum('body_type_enum', [
	'male_body',
	'female_body',
	'other_body_type'
]);

export const presentationStyleEnum = pgEnum('presentation_style_enum', [
	'conventional_masculine',
	'rugged_masculine',
	'feminine',
	'androgynous_neutral',
	'other_presentation_style'
]);

export const relationshipStatusEnum = pgEnum('relationship_status_enum', [
	'single',
	'in_a_relationship',
	'complicated',
	'open_relationship',
	'married',
	'polyamorous',
	'other_relationship_status',
	'prefer_not_to_say_relationship_status'
]);

export const completenessFilterEnum = pgEnum('completeness_filter_enum', [
	'has_avatar',
	'no_avatar',
	'is_profile_complete',
	'is_profile_incomplete',
	'is_verified',
	'is_unverified'
]);

export const matchStatusEnum = pgEnum('match_status_enum', ['liked', 'matched', 'blocked']);

export const pointTransactionTypeEnum = pgEnum('point_transaction_type_enum', [
	'registration_bonus',
	'daily_check_in',
	'profile_completion_bonus',
	'search_cost',
	'super_search_cost',
	'super_like_cost',
	'invite_bonus',
	'top_up',
	'system_adjustment'
]);

export const users = pgTable(
	'users',
	{
		id: uuid('id').primaryKey().defaultRandom(),
		telegramUserId: bigint('telegram_user_id', { mode: 'number' }).notNull().unique(),
		telegramUsername: text('telegram_username'),
		kinkMapCode: text('kink_map_code').notNull().unique(),
		nickname: text('nickname').notNull(),
		age: integer('age'),
		orientation: orientationEnum('orientation'),
		bodyType: bodyTypeEnum('body_type'),
		presentationStyle: presentationStyleEnum('presentation_style'),
		bio: text('bio'),
		heightCm: integer('height_cm'),
		weightKg: integer('weight_kg'),
		relationshipStatus: relationshipStatusEnum('relationship_status'),
		country: text('country'),
		city: text('city'),
		profileImageUrl: text('profile_image_url'),
		hasAvatar: boolean('has_avatar').notNull().default(false),
		profileCompletenessScore: integer('profile_completeness_score').notNull().default(0),
		isVerified: boolean('is_verified').notNull().default(false),
		trustScore: integer('trust_score').notNull().default(100),
		vipLevel: integer('vip_level').notNull().default(0),
		pointBalance: integer('point_balance').notNull().default(0),
		kinkCategoryBitmask: bigint('kink_category_bitmask', { mode: 'number' }).notNull().default(0),
		kinkRatings: jsonb('kink_ratings').notNull().default({}),
		blockVisibilityFromBodyTypes: bodyTypeEnum('block_visibility_from_body_types').array(),
		blockVisibilityFromPresentationStyles: presentationStyleEnum(
			'block_visibility_from_presentation_styles'
		).array(),
		blockVisibilityFromOrientations: orientationEnum('block_visibility_from_orientations').array(),
		createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
		updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
		lastActiveAt: timestamp('last_active_at', { withTimezone: true }).notNull().defaultNow(),
		isActive: boolean('is_active').notNull().default(true),
		isBanned: boolean('is_banned').notNull().default(false)
	},
	(table) => {
		return [
			index('idx_users_search_sorting').on(table.trustScore, table.lastActiveAt),
			index('idx_users_common_filters').on(
				table.age,
				table.country,
				table.city,
				table.presentationStyle
			),
			index('idx_users_kink_ratings_gin').on(table.kinkRatings),
			index('idx_users_block_body_types_gin').on(table.blockVisibilityFromBodyTypes),
			index('idx_users_block_presentation_styles_gin').on(
				table.blockVisibilityFromPresentationStyles
			),
			index('idx_users_block_orientations_gin').on(table.blockVisibilityFromOrientations)
		];
	}
);

export const matches = pgTable(
	'matches',
	{
		actorUserId: uuid('actor_user_id')
			.notNull()
			.references(() => users.id, { onDelete: 'cascade' }),
		targetUserId: uuid('target_user_id')
			.notNull()
			.references(() => users.id, { onDelete: 'cascade' }),
		status: matchStatusEnum('status').notNull(),
		createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
		updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow()
	},
	(table) => {
		return [
			primaryKey({
				columns: [table.actorUserId, table.targetUserId]
			}),
			index('idx_matches_incoming_interactions').on(table.targetUserId, table.status)
		];
	}
);

export const pointTransactions = pgTable(
	'point_transactions',
	{
		id: uuid('id').primaryKey().defaultRandom(),
		userId: uuid('user_id')
			.notNull()
			.references(() => users.id, { onDelete: 'cascade' }),
		amount: integer('amount').notNull(),
		type: pointTransactionTypeEnum('type').notNull(),
		description: text('description'),
		referenceId: text('reference_id'),
		createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow()
	},
	(table) => {
		return [
			index('idx_point_transactions_user_id').on(table.userId),
			index('idx_point_transactions_type').on(table.type)
		];
	}
);

export const usersRelations = relations(users, ({ many }) => ({
	interactionsInitiated: many(matches, { relationName: 'initiated_matches' }),
	interactionsReceived: many(matches, { relationName: 'received_matches' }),
	pointTransactions: many(pointTransactions)
}));

export const matchesRelations = relations(matches, ({ one }) => ({
	actor: one(users, {
		fields: [matches.actorUserId],
		references: [users.id],
		relationName: 'initiated_matches'
	}),
	target: one(users, {
		fields: [matches.targetUserId],
		references: [users.id],
		relationName: 'received_matches'
	})
}));

export const pointTransactionsRelations = relations(pointTransactions, ({ one }) => ({
	user: one(users, {
		fields: [pointTransactions.userId],
		references: [users.id]
	})
}));

export const user = pgTable('user', {
	id: text('id').primaryKey(),
	username: text('username').notNull().unique(),
	passwordHash: text('password_hash').notNull()
});

export const session = pgTable('session', {
	id: text('id').primaryKey(),
	userId: text('user_id')
		.notNull()
		.references(() => user.id),
	expiresAt: timestamp('expires_at', { withTimezone: true, mode: 'date' }).notNull()
});

export type Session = typeof session.$inferSelect;

export type User = typeof user.$inferSelect;
