<script lang="ts">
	import { searchOptions, searchActions } from '$lib/stores';
	import { ORIENTATIONS, BODY_TYPES, PRESENTATION_STYLES } from '$lib/utils/constants';
	import { slide } from 'svelte/transition';

	interface Props {
		searchMode: 'basic' | 'advanced' | 'super';
		onClose: () => void;
	}

	let { searchMode, onClose }: Props = $props();

	let options = $searchOptions;

	// 本地状态
	let ageRange = $state([18, 50]);
	let selectedOrientations = $state<string[]>([]);
	let selectedBodyTypes = $state<string[]>([]);
	let selectedPresentationStyles = $state<string[]>([]);
	let location = $state({ country: '', city: '', radius: 50 });

	function updateFilters() {
		const filters: any = {
			ageRange: ageRange as [number, number],
			location: location.country ? location : undefined,
			orientation: selectedOrientations.length > 0 ? selectedOrientations : undefined,
			bodyType: selectedBodyTypes.length > 0 ? selectedBodyTypes : undefined,
			presentationStyle:
				selectedPresentationStyles.length > 0 ? selectedPresentationStyles : undefined
		};

		searchActions.updateFilters(filters);
		onClose();
	}

	function resetFilters() {
		ageRange = [18, 50];
		selectedOrientations = [];
		selectedBodyTypes = [];
		selectedPresentationStyles = [];
		location = { country: '', city: '', radius: 50 };
	}

	function toggleSelection(array: string[], value: string) {
		const index = array.indexOf(value);
		if (index > -1) {
			array.splice(index, 1);
		} else {
			array.push(value);
		}
	}
</script>

<div
	transition:slide={{ duration: 300 }}
	class="border-b border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800"
>
	<div class="max-h-96 overflow-y-auto px-4 py-4">
		<!-- 头部 -->
		<div class="mb-4 flex items-center justify-between">
			<h3 class="text-lg font-semibold text-gray-900 dark:text-white">搜索过滤器</h3>
			<button onclick={onClose} class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
				<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M6 18L18 6M6 6l12 12"
					></path>
				</svg>
			</button>
		</div>

		<div class="space-y-6">
			<!-- 年龄范围 -->
			<div>
				<label class="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
					年龄范围: {ageRange[0]} - {ageRange[1]} 岁
				</label>
				<div class="flex items-center space-x-4">
					<input type="range" min="18" max="99" bind:value={ageRange[0]} class="flex-1" />
					<input type="range" min="18" max="99" bind:value={ageRange[1]} class="flex-1" />
				</div>
			</div>

			<!-- 位置 -->
			<div>
				<label class="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
					位置
				</label>
				<div class="grid grid-cols-2 gap-3">
					<input
						type="text"
						placeholder="国家"
						bind:value={location.country}
						class="rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
					/>
					<input
						type="text"
						placeholder="城市"
						bind:value={location.city}
						class="rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
					/>
				</div>
				<div class="mt-2">
					<label class="mb-1 block text-xs text-gray-600 dark:text-gray-400">
						搜索半径: {location.radius} km
					</label>
					<input type="range" min="1" max="500" bind:value={location.radius} class="w-full" />
				</div>
			</div>

			<!-- 性取向 -->
			<div>
				<label class="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
					性取向
				</label>
				<div class="grid grid-cols-2 gap-2">
					{#each ORIENTATIONS as orientation}
						<button
							onclick={() => toggleSelection(selectedOrientations, orientation.value)}
							class="rounded-lg border px-3 py-2 text-sm transition-colors {selectedOrientations.includes(
								orientation.value
							)
								? 'border-blue-500 bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300'
								: 'border-gray-300 text-gray-700 hover:border-gray-400 dark:border-gray-600 dark:text-gray-300 dark:hover:border-gray-500'}"
						>
							{orientation.label}
						</button>
					{/each}
				</div>
			</div>

			<!-- 身体类型 -->
			<div>
				<label class="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
					身体类型
				</label>
				<div class="grid grid-cols-1 gap-2">
					{#each BODY_TYPES as bodyType}
						<button
							onclick={() => toggleSelection(selectedBodyTypes, bodyType.value)}
							class="rounded-lg border px-3 py-2 text-sm transition-colors {selectedBodyTypes.includes(
								bodyType.value
							)
								? 'border-blue-500 bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300'
								: 'border-gray-300 text-gray-700 hover:border-gray-400 dark:border-gray-600 dark:text-gray-300 dark:hover:border-gray-500'}"
						>
							{bodyType.label}
						</button>
					{/each}
				</div>
			</div>

			<!-- 展现风格 -->
			<div>
				<label class="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
					展现风格
				</label>
				<div class="grid grid-cols-1 gap-2">
					{#each PRESENTATION_STYLES as style}
						<button
							onclick={() => toggleSelection(selectedPresentationStyles, style.value)}
							class="rounded-lg border px-3 py-2 text-sm transition-colors {selectedPresentationStyles.includes(
								style.value
							)
								? 'border-blue-500 bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300'
								: 'border-gray-300 text-gray-700 hover:border-gray-400 dark:border-gray-600 dark:text-gray-300 dark:hover:border-gray-500'}"
						>
							{style.label}
						</button>
					{/each}
				</div>
			</div>
		</div>

		<!-- 操作按钮 -->
		<div class="mt-6 flex space-x-3 border-t border-gray-200 pt-4 dark:border-gray-700">
			<button
				onclick={resetFilters}
				class="flex-1 rounded-lg border border-gray-300 px-4 py-2 text-gray-600 transition-colors hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
			>
				重置
			</button>
			<button
				onclick={updateFilters}
				class="flex-1 rounded-lg bg-blue-500 px-4 py-2 text-white transition-colors hover:bg-blue-600"
			>
				应用过滤器
			</button>
		</div>
	</div>
</div>
