<script lang="ts">
	import { goto } from '$app/navigation';
	import AdvancedProfileForm from '$lib/components/forms/AdvancedProfileForm.svelte';
	import { uiActions } from '$lib/stores';
	import type { PageData } from './$types';

	let { data }: { data: PageData } = $props();

	function handleSubmit(profileData: any) {
		// 显示成功消息
		uiActions.toast.success('高级偏好设置完成！现在可以使用高级搜索了');
		
		// 跳转到发现页面
		goto('/discover');
	}

	function handleCancel() {
		// 跳转到发现页面
		uiActions.toast.info('您可以稍后在设置中完善高级偏好');
		goto('/discover');
	}
</script>

<svelte:head>
	<title>高级偏好设置 - BlueX</title>
	<meta name="description" content="设置您的详细偏好以获得更精准的匹配" />
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
	<AdvancedProfileForm 
		data={data.form} 
		onSubmit={handleSubmit}
		onCancel={handleCancel}
	/>
</div>
